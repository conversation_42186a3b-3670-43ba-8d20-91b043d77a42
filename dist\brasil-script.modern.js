// ==UserScript==
// @name         SigPlus+ 🇧🇷
// @namespace    SigPlus+ 🇧🇷
// @version      1.0.30
// <AUTHOR>
// @description  Script for InfernoTrick by brazilians
// @license MIT
// @match        https://*.sigmally.com/*
// @icon         https://i.imgur.com/kgTrxZy.gif
// @grant        none
// @run-at       document-idle
// ==/UserScript==

// Compilado para: Moderno (ES2020+) (Para Tampermonkey 4.14+ com suporte a const/let/arrow functions)

"use strict";
// ===== CONFIGURAÇÕES E TIPOS =====
// @ts-ignore
const sigfix = window.sigfix;
// Configurações centralizadas
const CONFIG = {
	SCRIPT_NAME: "SigPlus",
	VERSION: "1.0.30",
	ANIMATION: {
		FADE_OUT: 200,
		FADE_IN: 10,
		TOAST_DURATION: 3000,
		TOAST_FADE: 300,
	},
	DURATIONS: {
		DISABLE_MOVE: 750,
		INPUT_CHECK_INTERVAL: 500,
		WAIT_ELEMENTS_INTERVAL: 500,
		INITIALIZATION_TIMEOUT: 10000,
	},
	STORAGE_KEYS: {
		double: "doubleKey",
		triple: "tripleKey",
		settings: "sigPlus_settings",
	},
	SELECTORS: {
		navbar: ".mod_menu_navbar",
		menuContent: ".mod_menu_content",
		chatInput: "#chatSendInput",
		nickInput: "#nick",
		tagInput: "#tag",
	},
};
// ===== SISTEMA DE TOAST =====
class ToastManager {
	constructor() {
		this.container = this.createContainer();
	}
	static getInstance() {
		if (!ToastManager.instance) {
			ToastManager.instance = new ToastManager();
		}
		return ToastManager.instance;
	}
	createContainer() {
		const container = document.createElement("div");
		container.id = "sigplus-toast-container";
		container.style.cssText = `
			position: fixed;
			top: 20px;
			right: 20px;
			z-index: 10000;
			pointer-events: none;
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		`;
		document.body.appendChild(container);
		return container;
	}
	show(message, options = {}) {
		const { type = "info", duration = CONFIG.ANIMATION.TOAST_DURATION, persistent = false } = options;
		const toast = document.createElement("div");
		toast.style.cssText = `
			background: ${this.getBackgroundColor(type)};
			color: white;
			padding: 12px 16px;
			border-radius: 8px;
			margin-bottom: 8px;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
			transform: translateX(100%);
			transition: transform ${CONFIG.ANIMATION.TOAST_FADE}ms ease-out;
			pointer-events: auto;
			max-width: 300px;
			word-wrap: break-word;
			font-size: 14px;
			line-height: 1.4;
		`;
		toast.innerHTML = `
			<div style="display: flex; align-items: center; gap: 8px;">
				<span style="font-size: 16px;">${this.getIcon(type)}</span>
				<span>${message}</span>
			</div>
		`;
		this.container.appendChild(toast);
		// Animação de entrada
		requestAnimationFrame(() => {
			toast.style.transform = "translateX(0)";
		});
		// Auto-remover se não for persistente
		if (!persistent) {
			setTimeout(() => this.remove(toast), duration);
		}
	}
	remove(toast) {
		toast.style.transform = "translateX(100%)";
		setTimeout(() => {
			if (toast.parentNode) {
				toast.parentNode.removeChild(toast);
			}
		}, CONFIG.ANIMATION.TOAST_FADE);
	}
	getBackgroundColor(type) {
		const colors = {
			success: "#10b981",
			error: "#ef4444",
			warning: "#f59e0b",
			info: "#3b82f6",
		};
		return colors[type];
	}
	getIcon(type) {
		const icons = {
			success: "✅",
			error: "❌",
			warning: "⚠️",
			info: "ℹ️",
		};
		return icons[type];
	}
}
// ===== SERVIÇOS UTILITÁRIOS =====
class StorageService {
	static get(key) {
		try {
			const value = localStorage.getItem(key);
			return value ? value : undefined;
		} catch (error) {
			console.error(`Erro ao ler localStorage para ${key}:`, error);
			return undefined;
		}
	}
	static set(key, value) {
		try {
			localStorage.setItem(key, value);
			return true;
		} catch (error) {
			console.error(`Erro ao salvar localStorage para ${key}:`, error);
			return false;
		}
	}
	static remove(key) {
		try {
			localStorage.removeItem(key);
			return true;
		} catch (error) {
			console.error(`Erro ao remover localStorage para ${key}:`, error);
			return false;
		}
	}
	static getJSON(key) {
		const raw = this.get(key);
		if (!raw) return undefined;
		try {
			return JSON.parse(raw);
		} catch (error) {
			console.error(`Erro ao fazer parse do JSON de ${key}:`, error);
			return undefined;
		}
	}
	static setJSON(key, value) {
		try {
			return this.set(key, JSON.stringify(value));
		} catch (error) {
			console.error(`Erro ao serializar JSON para ${key}:`, error);
			return false;
		}
	}
}
// ===== ESTADO GLOBAL =====
class AppState {
	constructor() {
		this.settings = {};
		this.isMacroDisabled = false;
	}
	static getInstance() {
		if (!AppState.instance) {
			AppState.instance = new AppState();
		}
		return AppState.instance;
	}
	getSettings() {
		return { ...this.settings };
	}
	getSetting(key) {
		return this.settings[key];
	}
	setSetting(key, value) {
		this.settings[key] = value;
	}
	removeSetting(key) {
		delete this.settings[key];
	}
	isMacroDisabledState() {
		return this.isMacroDisabled;
	}
	setMacroDisabled(disabled) {
		this.isMacroDisabled = disabled;
	}
	loadSettings() {
		Object.values(CONFIG.STORAGE_KEYS).forEach(key => {
			const value = StorageService.get(key);
			if (value) this.settings[key] = value.toLowerCase();
		});
	}
	saveSettings() {
		Object.values(CONFIG.STORAGE_KEYS).forEach(key => {
			if (this.settings[key]) {
				StorageService.set(key, this.settings[key]);
			} else {
				StorageService.remove(key);
			}
		});
	}
}
// Utilitário de criação de elementos
function createElement(tag, options = {}) {
	const el = document.createElement(tag);
	if (options.className) el.className = options.className;
	if (options.textContent) el.textContent = options.textContent;
	if (options.styles) Object.assign(el.style, options.styles);
	if (options.attributes) {
		Object.entries(options.attributes).forEach(([k, v]) => el.setAttribute(k, v));
	}
	return el;
}
// ===== GERENCIADOR DE KEYBINDS =====
class KeybindManager {
	constructor() {
		this.keyHandlers = {};
		this.appState = AppState.getInstance();
		this.toast = ToastManager.getInstance();
	}
	static getInstance() {
		if (!KeybindManager.instance) {
			KeybindManager.instance = new KeybindManager();
		}
		return KeybindManager.instance;
	}
	getKeybinds() {
		return [
			{
				property: CONFIG.STORAGE_KEYS.double,
				title: "Double Hover Key (2-4):",
				helpText: "Send Double HoverSplit",
			},
			{
				property: CONFIG.STORAGE_KEYS.triple,
				title: "Triple Hover Key (3-4):",
				helpText: "Send Triple(straight) HoverSplit",
			},
		];
	}
	updateKeyHandlers() {
		// Limpar handlers existentes primeiro
		Object.keys(this.keyHandlers).forEach(key => delete this.keyHandlers[key]);
		// Adicionar novos handlers
		const doubleKey = this.appState.getSetting(CONFIG.STORAGE_KEYS.double)?.toLowerCase();
		const tripleKey = this.appState.getSetting(CONFIG.STORAGE_KEYS.triple)?.toLowerCase();
		if (doubleKey) this.keyHandlers[doubleKey] = this.handleDoubleHellKey.bind(this);
		if (tripleKey) this.keyHandlers[tripleKey] = this.handleTripleHellKey.bind(this);
	}
	handleGlobalKeydown(event) {
		if (this.appState.isMacroDisabledState()) return;
		const pressedKey = event.key?.toLowerCase();
		const keyHandler = this.keyHandlers[pressedKey];
		if (keyHandler) keyHandler(event);
	}
	handleDoubleHellKey(event) {
		event.preventDefault();
		MacroExecutor.getInstance().executeHoverSplitSequence(2, 4);
	}
	handleTripleHellKey(event) {
		event.preventDefault();
		MacroExecutor.getInstance().executeHoverSplitSequence(3, 4);
	}
	getKeybindConfigs() {
		return this.getKeybinds();
	}
}
// ===== EXECUTOR DE MACROS =====
class MacroExecutor {
	constructor() {
		this.isDisabledMove = false;
		this.disableCountMove = 0;
		this.DEFAULT_KEY_EVENT_PROPS = {
			key: " ",
			code: "Space",
			keyCode: 32,
			which: 32,
			cancelable: true,
			composed: true,
		};
	}
	static getInstance() {
		if (!MacroExecutor.instance) {
			MacroExecutor.instance = new MacroExecutor();
		}
		return MacroExecutor.instance;
	}
	simulateKeyPress(eventProps) {
		window.dispatchEvent(new KeyboardEvent("keydown", eventProps));
		window.dispatchEvent(new KeyboardEvent("keyup", eventProps));
	}
	triggerSplit(times) {
		if (times <= 0) return;
		this.simulateKeyPress(this.DEFAULT_KEY_EVENT_PROPS);
		this.triggerSplit(times - 1);
	}
	triggerTabPress() {
		const view = sigfix.world.selected === sigfix.world.viewId.primary ? sigfix.world.viewId.secondary : sigfix.world.viewId.primary;
		sigfix.input.tab(view);
	}
	temporarilyDisableMove(duration, action) {
		if (!this.isDisabledMove) {
			this.originalMove = sigfix.net.move;
			sigfix.net.move = () => {};
			this.isDisabledMove = true;
		}
		this.disableCountMove++;
		try {
			action();
		} catch (error) {
			console.error("Error during action execution with movement disabled:", error);
			ToastManager.getInstance().show("Error executing macro", { type: "error" });
		} finally {
			setTimeout(() => {
				this.disableCountMove--;
				if (this.disableCountMove === 0 && this.originalMove) {
					sigfix.net.move = this.originalMove;
					this.isDisabledMove = false;
					this.originalMove = undefined;
				}
			}, duration);
		}
	}
	executeHoverSplitSequence(initialSplits, secondarySplits) {
		this.temporarilyDisableMove(CONFIG.DURATIONS.DISABLE_MOVE, () => {
			this.triggerSplit(initialSplits);
			this.triggerTabPress();
			this.triggerSplit(secondarySplits);
			setTimeout(() => this.triggerTabPress(), 0);
		});
	}
}
// ===== GERENCIADOR DE UI =====
class UIManager {
	constructor() {
		this.appState = AppState.getInstance();
		this.keybindManager = KeybindManager.getInstance();
		this.toast = ToastManager.getInstance();
		this.sigPlusContainer = this.createMainContainer();
	}
	static getInstance() {
		if (!UIManager.instance) {
			UIManager.instance = new UIManager();
		}
		return UIManager.instance;
	}
	createMainContainer() {
		return createElement("div", {
			className: "sigplus-container mod_tab scroll",
			styles: { display: "none" },
		});
	}
	getSigmodUsedKeys() {
		const sigmodKeys = [];
		try {
			const settings = StorageService.getJSON("SigModClient-settings");
			if (!settings?.macros?.keys) return sigmodKeys;
			const keys = settings.macros.keys;
			// Função auxiliar para processar chaves
			const processKeys = keyList => {
				keyList.filter(k => typeof k === "string" && k.length === 1).forEach(k => sigmodKeys.push(k.toLowerCase()));
			};
			// Chaves diretas
			processKeys([keys.rapidFeed, keys.respawn, keys.location, keys.saveImage]);
			// Processar grupos de chaves
			const processKeyGroup = group => {
				if (group && typeof group === "object") {
					processKeys(Object.values(group));
				}
			};
			// Processar cada grupo
			processKeyGroup(keys.splits);
			processKeyGroup(keys.line);
			processKeyGroup(keys.toggle);
			console.log("Keys used by SigMod:", sigmodKeys);
		} catch (error) {
			console.error("Error getting keys used by SigMod:", error);
		}
		return sigmodKeys;
	}
	createTitle(text) {
		this.sigPlusContainer.appendChild(createElement("div", { className: "text-center", textContent: text }));
	}
	createCategory({ title, emoji }) {
		const categoryContainer = createElement("div", { styles: { display: "flex", alignItems: "center", margin: "10px 0" } });
		categoryContainer.appendChild(createElement("span", { textContent: emoji, styles: { marginRight: "8px", fontSize: "1.2em" } }));
		categoryContainer.appendChild(createElement("span", { textContent: title, styles: { fontWeight: "bold", marginRight: "8px" } }));
		categoryContainer.appendChild(
			createElement("div", { styles: { flexGrow: "1", height: "1px", backgroundColor: "#bfbfbf", marginLeft: "8px" } })
		);
		this.sigPlusContainer.appendChild(categoryContainer);
	}
	createKeybindInput({ property, title, helpText }) {
		const inputContainer = createElement("div", {
			className: "keybind-input-container modRowItems justify-sb",
			styles: { position: "relative", padding: "5px 10px" },
		});
		inputContainer.title = helpText;
		inputContainer.appendChild(createElement("span", { textContent: title }));
		const input = createElement("input", {
			className: "keybind-input modInput",
			styles: { display: "flex", justifyContent: "center", textAlign: "center", alignItems: "center", width: "50px" },
			attributes: { type: "text", id: `brasil-${property}`, placeholder: "..." },
		});
		input.value = this.appState.getSetting(property)?.toUpperCase() ?? "";
		input.addEventListener("keydown", e => {
			e.preventDefault();
			const key = e.key;
			const keyLower = key.toLowerCase();
			if (key === "Escape" || key === "Backspace") {
				this.appState.removeSetting(property);
				input.value = "";
				this.appState.saveSettings();
				this.keybindManager.updateKeyHandlers();
				this.toast.show("Key removed", { type: "info" });
				return;
			}
			const keybinds = this.keybindManager.getKeybindConfigs();
			const isConflict = keybinds.some(kb => kb.property !== property && this.appState.getSetting(kb.property)?.toLowerCase() === keyLower);
			const sigmodUsed = this.getSigmodUsedKeys();
			if (isConflict) {
				this.toast.show(`Key "${key}" is already assigned to another command`, { type: "error" });
				return;
			}
			if (sigmodUsed.includes(keyLower)) {
				this.toast.show(`Key "${key}" is already used by SigMod!`, { type: "error" });
				return;
			}
			this.appState.setSetting(property, keyLower);
			input.value = key.toUpperCase();
			this.appState.saveSettings();
			this.keybindManager.updateKeyHandlers();
			this.toast.show(`Key "${key.toUpperCase()}" configured`, { type: "success" });
		});
		inputContainer.appendChild(input);
		this.sigPlusContainer.appendChild(inputContainer);
	}
	createNavigationButton(navigationMenu) {
		const navButton = createElement("button", { className: "sigplus-nav-btn mod_nav_btn" });
		navButton.appendChild(
			createElement("img", {
				styles: { width: "20px", height: "20px", verticalAlign: "middle", borderRadius: "50%" },
				attributes: { src: "https://imgur.com/caiOSAM.png", alt: "SigPlus Icon" },
			})
		);
		navButton.appendChild(createElement("span", { textContent: "SigPlus" }));
		navigationMenu.appendChild(navButton);
		navButton.addEventListener("click", () => {
			document.querySelectorAll(".mod_tab").forEach(tab => {
				const tabElement = tab;
				tabElement.style.opacity = "0";
				setTimeout(() => {
					tabElement.style.display = "none";
				}, CONFIG.ANIMATION.FADE_OUT);
			});
			document.querySelectorAll(".mod_nav_btn").forEach(btn => btn.classList.remove("mod_selected"));
			navButton.classList.add("mod_selected");
			setTimeout(() => {
				this.sigPlusContainer.style.display = "flex";
				setTimeout(() => {
					this.sigPlusContainer.style.opacity = "1";
				}, CONFIG.ANIMATION.FADE_IN);
			}, CONFIG.ANIMATION.FADE_OUT);
		});
	}
	createConfigContainer() {
		const navigationMenu = document.querySelector(CONFIG.SELECTORS.navbar);
		const menuContent = document.querySelector(CONFIG.SELECTORS.menuContent);
		if (!navigationMenu || !menuContent) return;
		navigationMenu.style.gap = "8px";
		this.createNavigationButton(navigationMenu);
		menuContent.appendChild(this.sigPlusContainer);
		this.createTitle("SigPlus Scripts");
		this.createCategory({ title: "HoverSplit", emoji: "✨" });
		const keybinds = this.keybindManager.getKeybindConfigs();
		keybinds.forEach(keybind => this.createKeybindInput(keybind));
	}
}
// ===== MONITOR DE INPUT =====
class InputMonitor {
	constructor() {
		this.appState = AppState.getInstance();
	}
	static getInstance() {
		if (!InputMonitor.instance) {
			InputMonitor.instance = new InputMonitor();
		}
		return InputMonitor.instance;
	}
	start() {
		const inputSelectors = [CONFIG.SELECTORS.chatInput, CONFIG.SELECTORS.nickInput, CONFIG.SELECTORS.tagInput];
		let focusCount = 0;
		const createDisableIndicator = () => {
			const indicator = createElement("div", {
				styles: {
					position: "fixed",
					top: "10px",
					left: "50%",
					transform: "translateX(-50%)",
					backgroundColor: "rgba(255, 0, 0, 0.8)",
					color: "white",
					padding: "8px 16px",
					borderRadius: "4px",
					fontSize: "14px",
					fontWeight: "bold",
					zIndex: "9999",
					display: "none",
				},
			});
			indicator.textContent = "Macros Disabled";
			document.body.appendChild(indicator);
			return indicator;
		};
		const disableIndicator = createDisableIndicator();
		const handleInputFocus = () => {
			focusCount++;
			if (focusCount === 1) {
				this.appState.setMacroDisabled(true);
				disableIndicator.style.display = "block";
			}
		};
		const handleInputBlur = () => {
			focusCount--;
			if (focusCount === 0) {
				this.appState.setMacroDisabled(false);
				disableIndicator.style.display = "none";
			}
		};
		setInterval(() => {
			inputSelectors.forEach(selector => {
				const input = document.querySelector(selector);
				if (input && !input.dataset.sigplusListenerAdded) {
					input.dataset.sigplusListenerAdded = "true";
					input.addEventListener("focus", handleInputFocus);
					input.addEventListener("blur", handleInputBlur);
				}
			});
		}, CONFIG.DURATIONS.INPUT_CHECK_INTERVAL);
	}
}
// ===== APLICAÇÃO PRINCIPAL =====
class SigPlusApp {
	constructor() {
		this.handleGlobalKeydown = event => {
			this.keybindManager.handleGlobalKeydown(event);
		};
		this.appState = AppState.getInstance();
		this.keybindManager = KeybindManager.getInstance();
		this.uiManager = UIManager.getInstance();
		this.toast = ToastManager.getInstance();
		this.inputMonitor = InputMonitor.getInstance();
	}
	static getInstance() {
		if (!SigPlusApp.instance) {
			SigPlusApp.instance = new SigPlusApp();
		}
		return SigPlusApp.instance;
	}
	waitForElements(selectors, callback, intervalTime = CONFIG.DURATIONS.WAIT_ELEMENTS_INTERVAL, maxAttempts = 20) {
		let attempts = 0;
		const interval = setInterval(() => {
			const allPresent = selectors.every(selector => document.querySelector(selector));
			if (allPresent) {
				clearInterval(interval);
				callback();
			} else {
				attempts++;
				if (attempts >= maxAttempts) {
					clearInterval(interval);
					console.warn(`Elements not found after ${maxAttempts} attempts.`);
				}
			}
		}, intervalTime);
	}
	async initialize() {
		try {
			// Carregar configurações
			this.appState.loadSettings();
			this.keybindManager.updateKeyHandlers();
			// Aguardar elementos da UI
			this.waitForElements([CONFIG.SELECTORS.navbar, CONFIG.SELECTORS.menuContent], () => this.uiManager.createConfigContainer());
			// Configurar event listeners
			document.addEventListener("keydown", this.handleGlobalKeydown);
			this.inputMonitor.start();
			// Mostrar toast de inicialização
			setTimeout(() => {
				this.toast.show(`${CONFIG.SCRIPT_NAME} v${CONFIG.VERSION} loaded!`, {
					type: "success",
					duration: 4000,
				});
			}, 1000);
			console.log(`${CONFIG.SCRIPT_NAME} v${CONFIG.VERSION} initialized successfully!`);
		} catch (error) {
			console.error("Error initializing SigPlus:", error);
			this.toast.show("Error initializing script", { type: "error" });
		}
	}
}
// ===== INICIALIZAÇÃO =====
const app = SigPlusApp.getInstance();
app.initialize();
