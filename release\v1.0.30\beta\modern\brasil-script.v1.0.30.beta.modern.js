// ==UserScript==
// @name         SigPlus+ 🇧🇷
// @namespace    SigPlus+ 🇧🇷
// @version      1.0.30
// <AUTHOR>
// @description  Script for InfernoTrick by brazilians
// @license MIT
// @match        https://*.sigmally.com/*
// @icon         https://i.imgur.com/kgTrxZy.gif
// @grant        none
// @run-at       document-idle
// ==/UserScript==

// Compilado para: Moderno (ES2020+) (Para Tampermonkey 4.14+ com suporte a const/let/arrow functions)

"use strict";
const sigfix = window.sigfix;
const ANIMATION_TIME = {
    FADE_OUT: 200,
    FADE_IN: 10,
};
const DURATIONS = {
    DISABLE_MOVE: 750,
    INPUT_CHECK_INTERVAL: 500,
    WAIT_ELEMENTS_INTERVAL: 500,
};
const STORAGE_KEYS = {
    double: "doubleKey",
    triple: "tripleKey",
};
const storageService = {
    get(key) {
        const value = localStorage.getItem(key);
        return value ? value : undefined;
    },
    set(key, value) {
        localStorage.setItem(key, value);
    },
    remove(key) {
        localStorage.removeItem(key);
    },
    getJSON(key) {
        const raw = this.get(key);
        if (!raw)
            return undefined;
        try {
            return JSON.parse(raw);
        }
        catch (error) {
            console.error(`Erro ao fazer parse do JSON de ${key}:`, error);
            return undefined;
        }
    },
};
const settings = {};
let isMacroDisabled = false;
const SELECTORS = {
    navbar: ".mod_menu_navbar",
    menuContent: ".mod_menu_content",
    chatInput: "#chatSendInput",
    nickInput: "#nick",
    tagInput: "#tag",
};
function createElement(tag, options = {}) {
    const el = document.createElement(tag);
    if (options.className)
        el.className = options.className;
    if (options.textContent)
        el.textContent = options.textContent;
    if (options.styles)
        Object.assign(el.style, options.styles);
    if (options.attributes) {
        Object.entries(options.attributes).forEach(([k, v]) => el.setAttribute(k, v));
    }
    return el;
}
function loadSettings() {
    Object.values(STORAGE_KEYS).forEach(key => {
        const value = storageService.get(key);
        if (value)
            settings[key] = value.toLowerCase();
    });
}
function saveSettings() {
    Object.values(STORAGE_KEYS).forEach(key => {
        if (settings[key]) {
            storageService.set(key, settings[key]);
        }
        else {
            storageService.remove(key);
        }
    });
}
const keybinds = [
    {
        property: STORAGE_KEYS.double,
        title: "Double Hover Key (2-4):",
        helpText: "Send Double HoverSplit",
    },
    {
        property: STORAGE_KEYS.triple,
        title: "Triple Hover Key (3-4):",
        helpText: "Send Triple(straight) HoverSplit",
    },
];
const keyHandlers = {};
function updateKeyHandlers() {
    Object.keys(keyHandlers).forEach(key => delete keyHandlers[key]);
    const doubleKey = settings[STORAGE_KEYS.double]?.toLowerCase();
    const tripleKey = settings[STORAGE_KEYS.triple]?.toLowerCase();
    if (doubleKey)
        keyHandlers[doubleKey] = handleDoubleHellKey;
    if (tripleKey)
        keyHandlers[tripleKey] = handleTripleHellKey;
}
const DEFAULT_KEY_EVENT_PROPS = {
    key: " ",
    code: "Space",
    keyCode: 32,
    which: 32,
    cancelable: true,
    composed: true,
};
function simulateKeyPress(eventProps) {
    window.dispatchEvent(new KeyboardEvent("keydown", eventProps));
    window.dispatchEvent(new KeyboardEvent("keyup", eventProps));
}
function triggerSplit(times) {
    if (times <= 0)
        return;
    simulateKeyPress(DEFAULT_KEY_EVENT_PROPS);
    triggerSplit(times - 1);
}
function triggerTabPress() {
    const view = sigfix.world.selected === sigfix.world.viewId.primary ? sigfix.world.viewId.secondary : sigfix.world.viewId.primary;
    sigfix.input.tab(view);
}
let isDisabledMove = false;
let disableCountMove = 0;
let originalMove;
function temporarilyDisableMove(duration, action) {
    if (!isDisabledMove) {
        originalMove = sigfix.net.move;
        sigfix.net.move = () => { };
        isDisabledMove = true;
    }
    disableCountMove++;
    try {
        action();
    }
    catch (error) {
        console.error("Erro durante execução da ação com movimento desabilitado:", error);
    }
    finally {
        setTimeout(() => {
            disableCountMove--;
            if (disableCountMove === 0 && originalMove) {
                sigfix.net.move = originalMove;
                isDisabledMove = false;
                originalMove = undefined;
            }
        }, duration);
    }
}
function handleDoubleHellKey(event) {
    event.preventDefault();
    executeHoverSplitSequence(2, 4);
}
function handleTripleHellKey(event) {
    event.preventDefault();
    executeHoverSplitSequence(3, 4);
}
function executeHoverSplitSequence(initialSplits, secondarySplits) {
    temporarilyDisableMove(DURATIONS.DISABLE_MOVE, () => {
        triggerSplit(initialSplits);
        triggerTabPress();
        triggerSplit(secondarySplits);
        setTimeout(() => triggerTabPress(), 0);
    });
}
const brasilScriptsContainer = createElement("div", {
    className: "brasil-scripts-container mod_tab scroll",
    styles: { display: "none" },
});
function createTitle(text) {
    brasilScriptsContainer.appendChild(createElement("div", { className: "text-center", textContent: text }));
}
function createCategory({ title, emoji }) {
    const categoryContainer = createElement("div", { styles: { display: "flex", alignItems: "center", margin: "10px 0" } });
    categoryContainer.appendChild(createElement("span", { textContent: emoji, styles: { marginRight: "8px", fontSize: "1.2em" } }));
    categoryContainer.appendChild(createElement("span", { textContent: title, styles: { fontWeight: "bold", marginRight: "8px" } }));
    categoryContainer.appendChild(createElement("div", { styles: { flexGrow: "1", height: "1px", backgroundColor: "#bfbfbf", marginLeft: "8px" } }));
    brasilScriptsContainer.appendChild(categoryContainer);
}
function getSigmodUsedKeys() {
    const sigmodKeys = [];
    try {
        const settings = storageService.getJSON("SigModClient-settings");
        if (!settings?.macros?.keys)
            return sigmodKeys;
        const keys = settings.macros.keys;
        const processKeys = (keyList) => {
            keyList.filter((k) => typeof k === "string" && k.length === 1).forEach(k => sigmodKeys.push(k.toLowerCase()));
        };
        processKeys([keys.rapidFeed, keys.respawn, keys.location, keys.saveImage]);
        const processKeyGroup = (group) => {
            if (group && typeof group === "object") {
                processKeys(Object.values(group));
            }
        };
        processKeyGroup(keys.splits);
        processKeyGroup(keys.line);
        processKeyGroup(keys.toggle);
        console.log("Teclas usadas pelo SigMod:", sigmodKeys);
    }
    catch (error) {
        console.error("Erro ao obter teclas usadas pelo SigMod:", error);
    }
    return sigmodKeys;
}
function createKeybindInput({ property, title, helpText }) {
    const inputContainer = createElement("div", {
        className: "keybind-input-container modRowItems justify-sb",
        styles: { position: "relative", padding: "5px 10px" },
    });
    inputContainer.title = helpText;
    inputContainer.appendChild(createElement("span", { textContent: title }));
    const input = createElement("input", {
        className: "keybind-input modInput",
        styles: { display: "flex", justifyContent: "center", textAlign: "center", alignItems: "center", width: "50px" },
        attributes: { type: "text", id: `brasil-${property}`, placeholder: "..." },
    });
    input.value = settings[property]?.toUpperCase() ?? "";
    input.addEventListener("keydown", e => {
        e.preventDefault();
        const key = e.key;
        const keyLower = key.toLowerCase();
        if (key === "Escape" || key === "Backspace") {
            settings[property] = "";
            input.value = "";
            saveSettings();
            updateKeyHandlers();
            return;
        }
        const isConflict = keybinds.some(kb => kb.property !== property && settings[kb.property]?.toLowerCase() === keyLower);
        const sigmodUsed = getSigmodUsedKeys();
        if (isConflict) {
            alert(`The key "${key}" is already assigned to another command.`);
            return;
        }
        if (sigmodUsed.includes(keyLower)) {
            alert(`The key "${key}" is already used by SigMod! Please choose another one.`);
            return;
        }
        settings[property] = keyLower;
        input.value = key.toUpperCase();
        saveSettings();
        updateKeyHandlers();
    });
    inputContainer.appendChild(input);
    brasilScriptsContainer.appendChild(inputContainer);
}
function createNavigationButton(navigationMenu) {
    const navButton = createElement("button", { className: "brasil-nav-btn mod_nav_btn" });
    navButton.appendChild(createElement("img", {
        styles: { width: "20px", height: "20px", verticalAlign: "middle", borderRadius: "50%" },
        attributes: { src: "https://imgur.com/caiOSAM.png", alt: "Ícone Brasil" },
    }));
    navButton.appendChild(createElement("span", { textContent: "Sig Plus+" }));
    navigationMenu.appendChild(navButton);
    navButton.addEventListener("click", () => {
        document.querySelectorAll(".mod_tab").forEach(tab => {
            const tabElement = tab;
            tabElement.style.opacity = "0";
            setTimeout(() => {
                tabElement.style.display = "none";
            }, ANIMATION_TIME.FADE_OUT);
        });
        document.querySelectorAll(".mod_nav_btn").forEach(btn => btn.classList.remove("mod_selected"));
        navButton.classList.add("mod_selected");
        setTimeout(() => {
            brasilScriptsContainer.style.display = "flex";
            setTimeout(() => {
                brasilScriptsContainer.style.opacity = "1";
            }, ANIMATION_TIME.FADE_IN);
        }, ANIMATION_TIME.FADE_OUT);
    });
}
function createConfigContainer() {
    const navigationMenu = document.querySelector(SELECTORS.navbar);
    const menuContent = document.querySelector(SELECTORS.menuContent);
    if (!navigationMenu || !menuContent)
        return;
    navigationMenu.style.gap = "8px";
    createNavigationButton(navigationMenu);
    menuContent.appendChild(brasilScriptsContainer);
    createTitle("Sigmally 🇧🇷 Scripts");
    createCategory({ title: "HoverSplit", emoji: "✨" });
    keybinds.forEach(createKeybindInput);
}
function monitorChatInput() {
    const inputSelectors = [SELECTORS.chatInput, SELECTORS.nickInput, SELECTORS.tagInput];
    let focusCount = 0;
    const createDisableIndicator = () => {
        const indicator = createElement("div", {
            styles: {
                display: "none",
                position: "fixed",
                bottom: "10px",
                right: "10px",
                padding: "5px 10px",
                backgroundColor: "rgba(192, 33, 33, 0.7)",
                color: "#fff",
                borderRadius: "5px",
                zIndex: "1000",
            },
        });
        indicator.id = "brasil-disable-indicator";
        indicator.textContent = "Desativado durante o chat";
        document.body.appendChild(indicator);
        return indicator;
    };
    const handleInputFocus = (indicator) => {
        focusCount++;
        if (focusCount === 1) {
            isMacroDisabled = true;
            indicator.style.display = "block";
        }
    };
    const handleInputBlur = (indicator) => {
        focusCount--;
        if (focusCount === 0) {
            isMacroDisabled = false;
            indicator.style.display = "none";
        }
    };
    const interval = setInterval(() => {
        const inputs = inputSelectors.flatMap(sel => Array.from(document.querySelectorAll(sel)));
        if (inputs.length > 0) {
            clearInterval(interval);
            const disableIndicator = createDisableIndicator();
            inputs.forEach(input => {
                input.addEventListener("focus", () => handleInputFocus(disableIndicator));
                input.addEventListener("blur", () => handleInputBlur(disableIndicator));
            });
        }
    }, DURATIONS.INPUT_CHECK_INTERVAL);
}
function waitForElements(selectors, callback, intervalTime = DURATIONS.WAIT_ELEMENTS_INTERVAL, maxAttempts = 20) {
    let attempts = 0;
    const interval = setInterval(() => {
        const allPresent = selectors.every(selector => document.querySelector(selector));
        if (allPresent) {
            clearInterval(interval);
            callback();
        }
        else {
            attempts++;
            if (attempts >= maxAttempts) {
                clearInterval(interval);
                console.warn(`waitForElements: Elementos não encontrados após ${maxAttempts} tentativas.`);
            }
        }
    }, intervalTime);
}
function handleGlobalKeydown(event) {
    if (isMacroDisabled)
        return;
    const pressedKey = event.key?.toLowerCase();
    const keyHandler = keyHandlers[pressedKey];
    if (keyHandler)
        keyHandler(event);
}
function initialize() {
    loadSettings();
    updateKeyHandlers();
    waitForElements([SELECTORS.navbar, SELECTORS.menuContent], createConfigContainer);
    document.addEventListener("keydown", handleGlobalKeydown);
    monitorChatInput();
}
initialize();