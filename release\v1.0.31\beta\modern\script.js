"use strict";
const sigfix = window.sigfix;
const CONFIG = {
    SCRIPT_NAME: "SigPlus+ 🇧🇷",
    VERSION: "1.0.30",
    ANIMATION: {
        FADE_OUT: 200,
        FADE_IN: 10,
        TOAST_DURATION: 3000,
        TOAST_FADE: 300,
    },
    DURATIONS: {
        DISABLE_MOVE: 750,
        INPUT_CHECK_INTERVAL: 500,
        WAIT_ELEMENTS_INTERVAL: 500,
        INITIALIZATION_TIMEOUT: 10000,
    },
    STORAGE_KEYS: {
        double: "doubleKey",
        triple: "tripleKey",
        settings: "brasilScripts_settings",
    },
    SELECTORS: {
        navbar: ".mod_menu_navbar",
        menuContent: ".mod_menu_content",
        chatInput: "#chatSendInput",
        nickInput: "#nick",
        tagInput: "#tag",
    },
};
class ToastManager {
    constructor() {
        this.container = this.createContainer();
    }
    static getInstance() {
        if (!ToastManager.instance) {
            ToastManager.instance = new ToastManager();
        }
        return ToastManager.instance;
    }
    createContainer() {
        const container = document.createElement("div");
        container.id = "brasil-toast-container";
        container.style.cssText = `
			position: fixed;
			top: 20px;
			right: 20px;
			z-index: 10000;
			pointer-events: none;
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		`;
        document.body.appendChild(container);
        return container;
    }
    show(message, options = {}) {
        const { type = "info", duration = CONFIG.ANIMATION.TOAST_DURATION, persistent = false } = options;
        const toast = document.createElement("div");
        toast.style.cssText = `
			background: ${this.getBackgroundColor(type)};
			color: white;
			padding: 12px 16px;
			border-radius: 8px;
			margin-bottom: 8px;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
			transform: translateX(100%);
			transition: transform ${CONFIG.ANIMATION.TOAST_FADE}ms ease-out;
			pointer-events: auto;
			max-width: 300px;
			word-wrap: break-word;
			font-size: 14px;
			line-height: 1.4;
		`;
        toast.innerHTML = `
			<div style="display: flex; align-items: center; gap: 8px;">
				<span style="font-size: 16px;">${this.getIcon(type)}</span>
				<span>${message}</span>
			</div>
		`;
        this.container.appendChild(toast);
        requestAnimationFrame(() => {
            toast.style.transform = "translateX(0)";
        });
        if (!persistent) {
            setTimeout(() => this.remove(toast), duration);
        }
    }
    remove(toast) {
        toast.style.transform = "translateX(100%)";
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, CONFIG.ANIMATION.TOAST_FADE);
    }
    getBackgroundColor(type) {
        const colors = {
            success: "#10b981",
            error: "#ef4444",
            warning: "#f59e0b",
            info: "#3b82f6",
        };
        return colors[type];
    }
    getIcon(type) {
        const icons = {
            success: "✅",
            error: "❌",
            warning: "⚠️",
            info: "ℹ️",
        };
        return icons[type];
    }
}
class StorageService {
    static get(key) {
        try {
            const value = localStorage.getItem(key);
            return value ? value : undefined;
        }
        catch (error) {
            console.error(`Erro ao ler localStorage para ${key}:`, error);
            return undefined;
        }
    }
    static set(key, value) {
        try {
            localStorage.setItem(key, value);
            return true;
        }
        catch (error) {
            console.error(`Erro ao salvar localStorage para ${key}:`, error);
            return false;
        }
    }
    static remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        }
        catch (error) {
            console.error(`Erro ao remover localStorage para ${key}:`, error);
            return false;
        }
    }
    static getJSON(key) {
        const raw = this.get(key);
        if (!raw)
            return undefined;
        try {
            return JSON.parse(raw);
        }
        catch (error) {
            console.error(`Erro ao fazer parse do JSON de ${key}:`, error);
            return undefined;
        }
    }
    static setJSON(key, value) {
        try {
            return this.set(key, JSON.stringify(value));
        }
        catch (error) {
            console.error(`Erro ao serializar JSON para ${key}:`, error);
            return false;
        }
    }
}
class AppState {
    constructor() {
        this.settings = {};
        this.isMacroDisabled = false;
    }
    static getInstance() {
        if (!AppState.instance) {
            AppState.instance = new AppState();
        }
        return AppState.instance;
    }
    getSettings() {
        return { ...this.settings };
    }
    getSetting(key) {
        return this.settings[key];
    }
    setSetting(key, value) {
        this.settings[key] = value;
    }
    removeSetting(key) {
        delete this.settings[key];
    }
    isMacroDisabledState() {
        return this.isMacroDisabled;
    }
    setMacroDisabled(disabled) {
        this.isMacroDisabled = disabled;
    }
    loadSettings() {
        Object.values(CONFIG.STORAGE_KEYS).forEach(key => {
            const value = StorageService.get(key);
            if (value)
                this.settings[key] = value.toLowerCase();
        });
    }
    saveSettings() {
        Object.values(CONFIG.STORAGE_KEYS).forEach(key => {
            if (this.settings[key]) {
                StorageService.set(key, this.settings[key]);
            }
            else {
                StorageService.remove(key);
            }
        });
    }
}
function createElement(tag, options = {}) {
    const el = document.createElement(tag);
    if (options.className)
        el.className = options.className;
    if (options.textContent)
        el.textContent = options.textContent;
    if (options.styles)
        Object.assign(el.style, options.styles);
    if (options.attributes) {
        Object.entries(options.attributes).forEach(([k, v]) => el.setAttribute(k, v));
    }
    return el;
}
class KeybindManager {
    constructor() {
        this.keyHandlers = {};
        this.appState = AppState.getInstance();
        this.toast = ToastManager.getInstance();
    }
    static getInstance() {
        if (!KeybindManager.instance) {
            KeybindManager.instance = new KeybindManager();
        }
        return KeybindManager.instance;
    }
    getKeybinds() {
        return [
            {
                property: CONFIG.STORAGE_KEYS.double,
                title: "Double Hover Key (2-4):",
                helpText: "Send Double HoverSplit",
            },
            {
                property: CONFIG.STORAGE_KEYS.triple,
                title: "Triple Hover Key (3-4):",
                helpText: "Send Triple(straight) HoverSplit",
            },
        ];
    }
    updateKeyHandlers() {
        Object.keys(this.keyHandlers).forEach(key => delete this.keyHandlers[key]);
        const doubleKey = this.appState.getSetting(CONFIG.STORAGE_KEYS.double)?.toLowerCase();
        const tripleKey = this.appState.getSetting(CONFIG.STORAGE_KEYS.triple)?.toLowerCase();
        if (doubleKey)
            this.keyHandlers[doubleKey] = this.handleDoubleHellKey.bind(this);
        if (tripleKey)
            this.keyHandlers[tripleKey] = this.handleTripleHellKey.bind(this);
    }
    handleGlobalKeydown(event) {
        if (this.appState.isMacroDisabledState())
            return;
        const pressedKey = event.key?.toLowerCase();
        const keyHandler = this.keyHandlers[pressedKey];
        if (keyHandler)
            keyHandler(event);
    }
    handleDoubleHellKey(event) {
        event.preventDefault();
        MacroExecutor.getInstance().executeHoverSplitSequence(2, 4);
    }
    handleTripleHellKey(event) {
        event.preventDefault();
        MacroExecutor.getInstance().executeHoverSplitSequence(3, 4);
    }
    getKeybindConfigs() {
        return this.getKeybinds();
    }
}
class MacroExecutor {
    constructor() {
        this.isDisabledMove = false;
        this.disableCountMove = 0;
        this.DEFAULT_KEY_EVENT_PROPS = {
            key: " ",
            code: "Space",
            keyCode: 32,
            which: 32,
            cancelable: true,
            composed: true,
        };
    }
    static getInstance() {
        if (!MacroExecutor.instance) {
            MacroExecutor.instance = new MacroExecutor();
        }
        return MacroExecutor.instance;
    }
    simulateKeyPress(eventProps) {
        window.dispatchEvent(new KeyboardEvent("keydown", eventProps));
        window.dispatchEvent(new KeyboardEvent("keyup", eventProps));
    }
    triggerSplit(times) {
        if (times <= 0)
            return;
        this.simulateKeyPress(this.DEFAULT_KEY_EVENT_PROPS);
        this.triggerSplit(times - 1);
    }
    triggerTabPress() {
        const view = sigfix.world.selected === sigfix.world.viewId.primary ? sigfix.world.viewId.secondary : sigfix.world.viewId.primary;
        sigfix.input.tab(view);
    }
    temporarilyDisableMove(duration, action) {
        if (!this.isDisabledMove) {
            this.originalMove = sigfix.net.move;
            sigfix.net.move = () => { };
            this.isDisabledMove = true;
        }
        this.disableCountMove++;
        try {
            action();
        }
        catch (error) {
            console.error("Erro durante execução da ação com movimento desabilitado:", error);
            ToastManager.getInstance().show("Erro ao executar macro", { type: "error" });
        }
        finally {
            setTimeout(() => {
                this.disableCountMove--;
                if (this.disableCountMove === 0 && this.originalMove) {
                    sigfix.net.move = this.originalMove;
                    this.isDisabledMove = false;
                    this.originalMove = undefined;
                }
            }, duration);
        }
    }
    executeHoverSplitSequence(initialSplits, secondarySplits) {
        this.temporarilyDisableMove(CONFIG.DURATIONS.DISABLE_MOVE, () => {
            this.triggerSplit(initialSplits);
            this.triggerTabPress();
            this.triggerSplit(secondarySplits);
            setTimeout(() => this.triggerTabPress(), 0);
        });
    }
}
const brasilScriptsContainer = createElement("div", {
    className: "brasil-scripts-container mod_tab scroll",
    styles: { display: "none" },
});
function createTitle(text) {
    brasilScriptsContainer.appendChild(createElement("div", { className: "text-center", textContent: text }));
}
function createCategory({ title, emoji }) {
    const categoryContainer = createElement("div", { styles: { display: "flex", alignItems: "center", margin: "10px 0" } });
    categoryContainer.appendChild(createElement("span", { textContent: emoji, styles: { marginRight: "8px", fontSize: "1.2em" } }));
    categoryContainer.appendChild(createElement("span", { textContent: title, styles: { fontWeight: "bold", marginRight: "8px" } }));
    categoryContainer.appendChild(createElement("div", { styles: { flexGrow: "1", height: "1px", backgroundColor: "#bfbfbf", marginLeft: "8px" } }));
    brasilScriptsContainer.appendChild(categoryContainer);
}
class UIManager {
    constructor() {
        this.appState = AppState.getInstance();
        this.keybindManager = KeybindManager.getInstance();
        this.toast = ToastManager.getInstance();
        this.brasilScriptsContainer = this.createMainContainer();
    }
    static getInstance() {
        if (!UIManager.instance) {
            UIManager.instance = new UIManager();
        }
        return UIManager.instance;
    }
    createMainContainer() {
        return createElement("div", {
            className: "brasil-scripts-container mod_tab scroll",
            styles: { display: "none" },
        });
    }
    getSigmodUsedKeys() {
        const sigmodKeys = [];
        try {
            const settings = StorageService.getJSON("SigModClient-settings");
            if (!settings?.macros?.keys)
                return sigmodKeys;
            const keys = settings.macros.keys;
            const processKeys = (keyList) => {
                keyList.filter((k) => typeof k === "string" && k.length === 1).forEach(k => sigmodKeys.push(k.toLowerCase()));
            };
            processKeys([keys.rapidFeed, keys.respawn, keys.location, keys.saveImage]);
            const processKeyGroup = (group) => {
                if (group && typeof group === "object") {
                    processKeys(Object.values(group));
                }
            };
            processKeyGroup(keys.splits);
            processKeyGroup(keys.line);
            processKeyGroup(keys.toggle);
            console.log("Teclas usadas pelo SigMod:", sigmodKeys);
        }
        catch (error) {
            console.error("Erro ao obter teclas usadas pelo SigMod:", error);
        }
        return sigmodKeys;
    }
    createTitle(text) {
        this.brasilScriptsContainer.appendChild(createElement("div", { className: "text-center", textContent: text }));
    }
    createCategory({ title, emoji }) {
        const categoryContainer = createElement("div", { styles: { display: "flex", alignItems: "center", margin: "10px 0" } });
        categoryContainer.appendChild(createElement("span", { textContent: emoji, styles: { marginRight: "8px", fontSize: "1.2em" } }));
        categoryContainer.appendChild(createElement("span", { textContent: title, styles: { fontWeight: "bold", marginRight: "8px" } }));
        categoryContainer.appendChild(createElement("div", { styles: { flexGrow: "1", height: "1px", backgroundColor: "#bfbfbf", marginLeft: "8px" } }));
        this.brasilScriptsContainer.appendChild(categoryContainer);
    }
    createKeybindInput({ property, title, helpText }) {
        const inputContainer = createElement("div", {
            className: "keybind-input-container modRowItems justify-sb",
            styles: { position: "relative", padding: "5px 10px" },
        });
        inputContainer.title = helpText;
        inputContainer.appendChild(createElement("span", { textContent: title }));
        const input = createElement("input", {
            className: "keybind-input modInput",
            styles: { display: "flex", justifyContent: "center", textAlign: "center", alignItems: "center", width: "50px" },
            attributes: { type: "text", id: `brasil-${property}`, placeholder: "..." },
        });
        input.value = this.appState.getSetting(property)?.toUpperCase() ?? "";
        input.addEventListener("keydown", e => {
            e.preventDefault();
            const key = e.key;
            const keyLower = key.toLowerCase();
            if (key === "Escape" || key === "Backspace") {
                this.appState.removeSetting(property);
                input.value = "";
                this.appState.saveSettings();
                this.keybindManager.updateKeyHandlers();
                this.toast.show("Tecla removida", { type: "info" });
                return;
            }
            const keybinds = this.keybindManager.getKeybindConfigs();
            const isConflict = keybinds.some(kb => kb.property !== property && this.appState.getSetting(kb.property)?.toLowerCase() === keyLower);
            const sigmodUsed = this.getSigmodUsedKeys();
            if (isConflict) {
                this.toast.show(`A tecla "${key}" já está atribuída a outro comando`, { type: "error" });
                return;
            }
            if (sigmodUsed.includes(keyLower)) {
                this.toast.show(`A tecla "${key}" já é usada pelo SigMod!`, { type: "error" });
                return;
            }
            this.appState.setSetting(property, keyLower);
            input.value = key.toUpperCase();
            this.appState.saveSettings();
            this.keybindManager.updateKeyHandlers();
            this.toast.show(`Tecla "${key.toUpperCase()}" configurada`, { type: "success" });
        });
        inputContainer.appendChild(input);
        this.brasilScriptsContainer.appendChild(inputContainer);
    }
}
function createNavigationButton(navigationMenu) {
    const navButton = createElement("button", { className: "brasil-nav-btn mod_nav_btn" });
    navButton.appendChild(createElement("img", {
        styles: { width: "20px", height: "20px", verticalAlign: "middle", borderRadius: "50%" },
        attributes: { src: "https://imgur.com/caiOSAM.png", alt: "Ícone Brasil" },
    }));
    navButton.appendChild(createElement("span", { textContent: "Sig Plus+" }));
    navigationMenu.appendChild(navButton);
    navButton.addEventListener("click", () => {
        document.querySelectorAll(".mod_tab").forEach(tab => {
            const tabElement = tab;
            tabElement.style.opacity = "0";
            setTimeout(() => {
                tabElement.style.display = "none";
            }, ANIMATION_TIME.FADE_OUT);
        });
        document.querySelectorAll(".mod_nav_btn").forEach(btn => btn.classList.remove("mod_selected"));
        navButton.classList.add("mod_selected");
        setTimeout(() => {
            brasilScriptsContainer.style.display = "flex";
            setTimeout(() => {
                brasilScriptsContainer.style.opacity = "1";
            }, ANIMATION_TIME.FADE_IN);
        }, ANIMATION_TIME.FADE_OUT);
    });
}
function createConfigContainer() {
    const navigationMenu = document.querySelector(SELECTORS.navbar);
    const menuContent = document.querySelector(SELECTORS.menuContent);
    if (!navigationMenu || !menuContent)
        return;
    navigationMenu.style.gap = "8px";
    createNavigationButton(navigationMenu);
    menuContent.appendChild(brasilScriptsContainer);
    createTitle("Sigmally 🇧🇷 Scripts");
    createCategory({ title: "HoverSplit", emoji: "✨" });
    keybinds.forEach(createKeybindInput);
}
function monitorChatInput() {
    const inputSelectors = [SELECTORS.chatInput, SELECTORS.nickInput, SELECTORS.tagInput];
    let focusCount = 0;
    const createDisableIndicator = () => {
        const indicator = createElement("div", {
            styles: {
                display: "none",
                position: "fixed",
                bottom: "10px",
                right: "10px",
                padding: "5px 10px",
                backgroundColor: "rgba(192, 33, 33, 0.7)",
                color: "#fff",
                borderRadius: "5px",
                zIndex: "1000",
            },
        });
        indicator.id = "brasil-disable-indicator";
        indicator.textContent = "Desativado durante o chat";
        document.body.appendChild(indicator);
        return indicator;
    };
    const handleInputFocus = (indicator) => {
        focusCount++;
        if (focusCount === 1) {
            isMacroDisabled = true;
            indicator.style.display = "block";
        }
    };
    const handleInputBlur = (indicator) => {
        focusCount--;
        if (focusCount === 0) {
            isMacroDisabled = false;
            indicator.style.display = "none";
        }
    };
    const interval = setInterval(() => {
        const inputs = inputSelectors.flatMap(sel => Array.from(document.querySelectorAll(sel)));
        if (inputs.length > 0) {
            clearInterval(interval);
            const disableIndicator = createDisableIndicator();
            inputs.forEach(input => {
                input.addEventListener("focus", () => handleInputFocus(disableIndicator));
                input.addEventListener("blur", () => handleInputBlur(disableIndicator));
            });
        }
    }, DURATIONS.INPUT_CHECK_INTERVAL);
}
function waitForElements(selectors, callback, intervalTime = DURATIONS.WAIT_ELEMENTS_INTERVAL, maxAttempts = 20) {
    let attempts = 0;
    const interval = setInterval(() => {
        const allPresent = selectors.every(selector => document.querySelector(selector));
        if (allPresent) {
            clearInterval(interval);
            callback();
        }
        else {
            attempts++;
            if (attempts >= maxAttempts) {
                clearInterval(interval);
                console.warn(`waitForElements: Elementos não encontrados após ${maxAttempts} tentativas.`);
            }
        }
    }, intervalTime);
}
function handleGlobalKeydown(event) {
    if (isMacroDisabled)
        return;
    const pressedKey = event.key?.toLowerCase();
    const keyHandler = keyHandlers[pressedKey];
    if (keyHandler)
        keyHandler(event);
}
function initialize() {
    loadSettings();
    updateKeyHandlers();
    waitForElements([SELECTORS.navbar, SELECTORS.menuContent], createConfigContainer);
    document.addEventListener("keydown", handleGlobalKeydown);
    monitorChatInput();
}
class InputMonitor {
    constructor() {
        this.appState = AppState.getInstance();
    }
    static getInstance() {
        if (!InputMonitor.instance) {
            InputMonitor.instance = new InputMonitor();
        }
        return InputMonitor.instance;
    }
    start() {
        const inputSelectors = [CONFIG.SELECTORS.chatInput, CONFIG.SELECTORS.nickInput, CONFIG.SELECTORS.tagInput];
        let focusCount = 0;
        const createDisableIndicator = () => {
            const indicator = createElement("div", {
                styles: {
                    position: "fixed",
                    top: "10px",
                    left: "50%",
                    transform: "translateX(-50%)",
                    backgroundColor: "rgba(255, 0, 0, 0.8)",
                    color: "white",
                    padding: "8px 16px",
                    borderRadius: "4px",
                    fontSize: "14px",
                    fontWeight: "bold",
                    zIndex: "9999",
                    display: "none",
                },
            });
            indicator.textContent = "Macros Desabilitados";
            document.body.appendChild(indicator);
            return indicator;
        };
        const disableIndicator = createDisableIndicator();
        const handleInputFocus = () => {
            focusCount++;
            if (focusCount === 1) {
                this.appState.setMacroDisabled(true);
                disableIndicator.style.display = "block";
            }
        };
        const handleInputBlur = () => {
            focusCount--;
            if (focusCount === 0) {
                this.appState.setMacroDisabled(false);
                disableIndicator.style.display = "none";
            }
        };
        setInterval(() => {
            inputSelectors.forEach(selector => {
                const input = document.querySelector(selector);
                if (input && !input.dataset.brasilListenerAdded) {
                    input.dataset.brasilListenerAdded = "true";
                    input.addEventListener("focus", handleInputFocus);
                    input.addEventListener("blur", handleInputBlur);
                }
            });
        }, CONFIG.DURATIONS.INPUT_CHECK_INTERVAL);
    }
}
class BrasilScriptsApp {
    constructor() {
        this.handleGlobalKeydown = (event) => {
            this.keybindManager.handleGlobalKeydown(event);
        };
        this.appState = AppState.getInstance();
        this.keybindManager = KeybindManager.getInstance();
        this.uiManager = UIManager.getInstance();
        this.toast = ToastManager.getInstance();
        this.inputMonitor = InputMonitor.getInstance();
    }
    static getInstance() {
        if (!BrasilScriptsApp.instance) {
            BrasilScriptsApp.instance = new BrasilScriptsApp();
        }
        return BrasilScriptsApp.instance;
    }
    waitForElements(selectors, callback, intervalTime = CONFIG.DURATIONS.WAIT_ELEMENTS_INTERVAL, maxAttempts = 20) {
        let attempts = 0;
        const interval = setInterval(() => {
            const allPresent = selectors.every(selector => document.querySelector(selector));
            if (allPresent) {
                clearInterval(interval);
                callback();
            }
            else {
                attempts++;
                if (attempts >= maxAttempts) {
                    clearInterval(interval);
                    console.warn(`Elementos não encontrados após ${maxAttempts} tentativas.`);
                }
            }
        }, intervalTime);
    }
    async initialize() {
        try {
            this.appState.loadSettings();
            this.keybindManager.updateKeyHandlers();
            this.waitForElements([CONFIG.SELECTORS.navbar, CONFIG.SELECTORS.menuContent], () => this.uiManager.createConfigContainer());
            document.addEventListener("keydown", this.handleGlobalKeydown);
            this.inputMonitor.start();
            setTimeout(() => {
                this.toast.show(`${CONFIG.SCRIPT_NAME} v${CONFIG.VERSION} carregado!`, {
                    type: "success",
                    duration: 4000
                });
            }, 1000);
            console.log(`${CONFIG.SCRIPT_NAME} v${CONFIG.VERSION} inicializado com sucesso!`);
        }
        catch (error) {
            console.error("Erro ao inicializar Brasil Scripts:", error);
            this.toast.show("Erro ao inicializar script", { type: "error" });
        }
    }
}
const app = BrasilScriptsApp.getInstance();
app.initialize();
