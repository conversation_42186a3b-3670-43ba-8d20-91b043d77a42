{"compilerOptions": {"target": "ES2020", "module": "none", "moduleResolution": "node", "strict": true, "outDir": "./dist", "lib": ["DOM", "ES2020"], "esModuleInterop": true, "skipLibCheck": true, "typeRoots": ["./src/types", "node_modules/@types"], "useDefineForClassFields": false, "removeComments": false, "preserveConstEnums": true, "declaration": false, "sourceMap": false, "inlineSourceMap": false, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "release"]}