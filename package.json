{"name": "script", "version": "1.0.32", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "node build.js", "build:config": "node build-config.mjs"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^22.15.17", "chalk": "^5.4.1", "chalk-animation": "^2.0.3", "enquirer": "^2.4.1", "eslint": "^9.17.0", "figlet": "^1.8.0", "ora": "^8.1.1", "prettier": "^3.4.2", "rimraf": "^6.0.1", "typescript": "^5.7.2", "undici": "^7.9.0"}, "dependencies": {"cli-progress": "^3.12.0", "fs": "^0.0.1-security", "inquirer": "^8.2.6", "semver": "^7.6.3"}}