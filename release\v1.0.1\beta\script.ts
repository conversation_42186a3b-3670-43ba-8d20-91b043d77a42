// ==UserScript==
// @name         BrasilScripts 🇧🇷
// @namespace    BrasilScripts 🇧🇷
// @version      1.0.1
// author        kevinkvn_
// @description  Brasil 🇧🇷 Scripts para sigmally
// @match        https://one.sigmally.com
// @icon         https://i.imgur.com/gpwZ3ur.gif
// @grant        none
// ==/UserScript==

(function (): void {
	"use strict";

	const STORAGE_KEYS = {
		double: "doubleKey",
		triple: "tripleKey",
	} as const;
	type LocalStorageKey = (typeof STORAGE_KEYS)[keyof typeof STORAGE_KEYS];
	const KEY_SPLIT_PROPS = {
		key: " ",
		code: "Space",
		keyCode: 32,
		which: 32,
		cancelable: true,
		composed: true,
		isTrusted: true,
	};

	const TAB_KEY_PROPS = {
		key: "Tab",
		code: "Tab",
	};

	let isMacroDisabled = false;

	function getLocalStorageKey(key: LocalStorageKey): string | null {
		return localStorage.getItem(key);
	}

	function setLocalStorageKey(key: LocalStorageKey, value: string): void {
		localStorage.setItem(key, value);
	}

	function removeLocalStorageKey(key: LocalStorageKey): void {
		localStorage.removeItem(key);
	}

	function split(times: number): void {
		if (times <= 0) return;
		window.dispatchEvent(new KeyboardEvent("keydown", KEY_SPLIT_PROPS));
		window.dispatchEvent(new KeyboardEvent("keyup", KEY_SPLIT_PROPS));
		split(times - 1);
	}

	function pressTab(): void {
		window.dispatchEvent(new KeyboardEvent("keydown", TAB_KEY_PROPS));
		window.dispatchEvent(new KeyboardEvent("keyup", TAB_KEY_PROPS));
	}

	function injectStyles(): void {
		const style = document.createElement("style");
		style.textContent = `
		@keyframes neon-glow {
		  0%   { box-shadow: 0 0 6px #01ff70; }
		  100% { box-shadow: 0 0 15px #01ff70; }
		}
  
		#inferno-config-minimalista {
		  z-index: 9999999;
		  position: absolute;
		  top: 20px; left: 20px;
		  width: 240px;
		  padding: 15px;
		  display: flex;
		  flex-direction: column;
		  align-items: center;
		  justify-content: center;
  
		  background: linear-gradient(135deg, #151515, #1A0E2D);
		  color: #ffffff;
		  border: 2px solid #01ff70;
		  border-radius: 10px;
		  font-family: Arial, sans-serif;
		  box-sizing: border-box;
		  animation: neon-glow 2s ease-in-out infinite alternate;
		  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
		}
  
		#inferno-config-minimalista h3 {
		  margin: 0 0 12px;
		  font-size: 20px;
		  text-align: center;
		  color: rgba(1, 255, 112, 0.8);
		  text-shadow: 0 0 6px #01ff70;
		}
  
		#inferno-config-minimalista p {
		  margin: 0 0 8px;
		  font-size: 14px;
		  line-height: 1.4;
		  color: #f8f8f2;
		  text-align: center;
		}
  
		.campo-config {
		  display: flex;
		  flex-direction: column;
		  align-items: center;
		  margin-bottom: 12px;
		}
  
		#inferno-config-minimalista input {
		  width: 120px;
		  max-width: 120px;
		  padding: 6px;
		  border: 1px solid #01ff70;
		  border-radius: 4px;
		  background-color: rgba(0, 0, 0, 0.3);
		  color: #fff;
		  outline: none;
		  text-align: center;
		  transition: all 0.2s ease;
  
		  white-space: nowrap;
		  overflow: hidden;
		  text-overflow: ellipsis;
		}
  
		#inferno-config-minimalista input:hover {
		  background-color: rgba(0, 0, 0, 0.5);
		}
  
		#inferno-config-minimalista input:focus {
		  box-shadow: 0 0 8px #01ff70;
		  background-color: rgba(0, 0, 0, 0.6);
		}
  
		#inferno-sign {
		  position: absolute;
		  bottom: 4px;
		  right: 6px;
		  font-size: 10px;
		  color: #ffffff90;
		  font-family: Arial, sans-serif;
		  pointer-events: none;
		}

		        #inferno-disable-indicator {
          position: absolute;
          top: 15px;
          right: 15px;
          color: #fff;
          background-color: rgba(255, 0, 0, 0.8);
          padding: 6px 10px;
          border: 1px solid #ff5757;
          border-radius: 4px;
          font-size: 12px;
          font-family: Arial, sans-serif;
          display: none;
          z-index: 99999999;
          text-transform: uppercase;
          box-shadow: 0 0 6px rgba(255, 0, 0, 0.5);
        }
	  `;
		document.head.appendChild(style);
	}

	function createConfigContainer(): void {
		const overlays = document.getElementById("overlays");
		if (!overlays) return;

		if (document.getElementById("inferno-config-minimalista")) return;

		injectStyles();

		const container = document.createElement("div");
		container.id = "inferno-config-minimalista";

		const title = document.createElement("h3");
		title.textContent = "InfernoSplit";
		container.appendChild(title);

		function createConfigField(labelText: string, placeholder: string, storageKey: LocalStorageKey): HTMLInputElement {
			const fieldWrapper = document.createElement("div");
			fieldWrapper.className = "campo-config";

			const label = document.createElement("p");
			label.textContent = labelText;

			const input = document.createElement("input");
			input.type = "text";
			input.placeholder = placeholder;

			const savedKey = getLocalStorageKey(storageKey);
			if (savedKey) {
				input.value = savedKey;
			}

			input.addEventListener("keydown", (e: KeyboardEvent) => {
				e.preventDefault();
				const pressedKey = e.key;

				const otherKey =
					storageKey === STORAGE_KEYS.double ? getLocalStorageKey(STORAGE_KEYS.triple) : getLocalStorageKey(STORAGE_KEYS.double);

				if (otherKey && otherKey === pressedKey) {
					const keyToRemove = storageKey === STORAGE_KEYS.double ? STORAGE_KEYS.triple : STORAGE_KEYS.double;
					removeLocalStorageKey(keyToRemove);

					const otherInput = container.querySelectorAll("input")[storageKey === STORAGE_KEYS.double ? 1 : 0] as HTMLInputElement;
					if (otherInput) {
						otherInput.value = "";
					}
				}

				setLocalStorageKey(storageKey, pressedKey);
				input.value = pressedKey;
			});

			fieldWrapper.appendChild(label);
			fieldWrapper.appendChild(input);
			container.appendChild(fieldWrapper);

			return input;
		}

		createConfigField("Tecla para Double (2):", "Double Key...", STORAGE_KEYS.double);

		createConfigField("Tecla para Triple (3):", "Triple Key...", STORAGE_KEYS.triple);

		const sign = document.createElement("div");
		sign.id = "inferno-sign";
		sign.textContent = "by kevinkvn_";
		container.appendChild(sign);

		overlays.appendChild(container);

		const disableIndicator = document.createElement("div");
		disableIndicator.id = "inferno-disable-indicator";
		disableIndicator.textContent = "Desativado durante o chat";
		document.body.appendChild(disableIndicator);
	}

	function handleGlobalKeydown(e: KeyboardEvent): void {
		if (isMacroDisabled) return;
		const doubleKey = getLocalStorageKey(STORAGE_KEYS.double);
		const tripleKey = getLocalStorageKey(STORAGE_KEYS.triple);

		if (!doubleKey && !tripleKey) return;

		if (e.key === doubleKey) {
			e.preventDefault();
			split(2);
			pressTab();
			split(4);
		}

		if (e.key === tripleKey) {
			e.preventDefault();
			split(3);
			pressTab();
			split(4);
		}
	}

	function waitForChatInputAndBind(): void {
		const interval = setInterval(() => {
			const chatSendInput = document.getElementById("chatSendInput") as HTMLInputElement | null;
			if (chatSendInput) {
				clearInterval(interval);
				const disableIndicator = document.getElementById("inferno-disable-indicator") as HTMLDivElement | null;
				if (disableIndicator) {
					chatSendInput.addEventListener("focus", () => {
						isMacroDisabled = true;
						disableIndicator.style.display = "block";
					});
					chatSendInput.addEventListener("blur", () => {
						isMacroDisabled = false;
						disableIndicator.style.display = "none";
					});
				}
			}
		}, 500);
	}

	function init(): void {
		createConfigContainer();
		document.addEventListener("keydown", handleGlobalKeydown);

		waitForChatInputAndBind();
		console.log("InfernoScript com double/triple e exclusividade de teclas - Loaded.");
	}

	init();
})();
