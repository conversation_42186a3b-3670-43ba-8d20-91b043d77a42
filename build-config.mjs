#!/usr/bin/env node

import chalk from "chalk";
import chalkAnimation from "chalk-animation";
import { exec } from "child_process";
import pkg from "enquirer";
import figlet from "figlet";
import fs from "fs";
import ora from "ora";
import path from "path";
import semver from "semver";
import { fileURLToPath } from "url";

const { prompt } = pkg;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuração moderna e extensível
const PROJECT_CONFIG = {
	paths: {
		packageJson: path.resolve(__dirname, "package.json"),
		packageLockJson: path.resolve(__dirname, "package-lock.json"),
		inputScript: path.resolve(__dirname, "src", "script.ts"),
		distDir: path.resolve(__dirname, "dist"),
		releaseDir: path.resolve(__dirname, "release"),
		templatesDir: path.resolve(__dirname, "templates"),
		configDir: path.resolve(__dirname, "build-configs"),
	},
	scriptName: "brasil-script",
	banner: {
		title: "SigPlus Builder",
		font: "Standard",
		width: 80,
	},
	// Configurações de compilação por target
	compilationTargets: {
		modern: {
			name: "Moderno (ES2020+)",
			description: "Para Tampermonkey 4.14+ com suporte a const/let/arrow functions",
			tsConfig: {
				target: "ES2020",
				module: "none",
				lib: ["DOM", "ES2020"],
				useDefineForClassFields: false,
				strict: true,
			},
			features: ["const", "let", "arrow-functions", "template-literals", "destructuring"],
		},
		compatible: {
			name: "Compatível (ES2015)",
			description: "Para Tampermonkey 4.0+ com boa compatibilidade",
			tsConfig: {
				target: "ES2015",
				module: "none",
				lib: ["DOM", "ES2015"],
				strict: true,
			},
			features: ["const", "let", "arrow-functions", "template-literals"],
		},
		legacy: {
			name: "Legado (ES5)",
			description: "Para versões antigas do Tampermonkey",
			tsConfig: {
				target: "ES5",
				module: "none",
				lib: ["DOM", "ES5"],
				strict: true,
			},
			features: ["var-only", "function-expressions"],
		},
	},
};

// Utilitários modernos
function createTsConfig(target, outDir, inputPath) {
	const targetConfig = PROJECT_CONFIG.compilationTargets[target];
	if (!targetConfig) {
		throw new Error(`Target de compilação '${target}' não encontrado`);
	}

	// Determinar se é um arquivo específico ou diretório
	const isSpecificFile = inputPath && inputPath.endsWith(".ts");

	return {
		compilerOptions: {
			...targetConfig.tsConfig,
			outDir,
			moduleResolution: "node",
			esModuleInterop: true,
			skipLibCheck: true,
			typeRoots: ["./src/types", "node_modules/@types"],
			removeComments: false, // Controlado separadamente
		},
		include: isSpecificFile ? [inputPath] : ["src/**/*"],
		exclude: ["node_modules", "dist", "release"],
	};
}

function validateTampermonkeyCompatibility(target, scriptContent) {
	const targetConfig = PROJECT_CONFIG.compilationTargets[target];
	const issues = [];

	// Verificar recursos não suportados
	if (target === "legacy") {
		if (scriptContent.includes("const ") || scriptContent.includes("let ")) {
			issues.push("⚠️ const/let detectados - podem não funcionar em versões antigas");
		}
		if (scriptContent.includes("=>")) {
			issues.push("⚠️ Arrow functions detectadas - podem não funcionar em versões antigas");
		}
	}

	// Verificar @grant requirements
	const grantMatch = scriptContent.match(/@grant\s+(.+)/g);
	if (grantMatch) {
		const grants = grantMatch.map(g => g.replace(/@grant\s+/, "").trim());
		if (grants.includes("none") && grants.length > 1) {
			issues.push("⚠️ @grant none com outras permissões pode causar conflitos");
		}
	}

	return {
		compatible: issues.length === 0,
		issues,
		target: targetConfig.name,
	};
}

function displayBanner() {
	return new Promise((resolve, reject) => {
		figlet.text(
			PROJECT_CONFIG.banner.title,
			{
				font: PROJECT_CONFIG.banner.font,
				horizontalLayout: "default",
				verticalLayout: "default",
				width: PROJECT_CONFIG.banner.width,
				whitespaceBreak: true,
			},
			(err, data) => {
				if (err) {
					console.error(chalk.red("❌ Erro ao gerar o banner:"));
					console.dir(err);
					reject(err);
					return;
				}
				const brasilFlag = "🇧🇷";
				const flagLine = chalk.green(brasilFlag) + " ".repeat(data.split("\n")[0].length - 2);
				console.log(flagLine);
				console.log(chalk.cyan(data));
				console.log(chalk.gray("🚀 Sistema de Build Moderno para Tampermonkey"));
				console.log();
				resolve();
			}
		);
	});
}

async function welcomeAnimation() {
	const spinner = ora({
		text: "🎉 Bem-vindo ao Build Script do BrasilScripts! 🎉",
		spinner: "dots12",
		color: "cyan",
	}).start();
	await new Promise(resolve => setTimeout(resolve, 2000));
	spinner.stop();
}

function incrementVersion(currentVersion) {
	const newVersion = semver.inc(currentVersion, "patch");
	if (!newVersion) {
		throw new Error("Erro ao incrementar a versão.");
	}
	return newVersion;
}

function updatePackageJson(newVersion) {
	try {
		const packageJson = JSON.parse(fs.readFileSync(PROJECT_CONFIG.paths.packageJson, "utf-8"));
		packageJson.version = newVersion;
		fs.writeFileSync(PROJECT_CONFIG.paths.packageJson, JSON.stringify(packageJson, null, 2), "utf-8");
	} catch (error) {
		throw new Error(`Falha ao atualizar package.json: ${error.message}`);
	}
}

function updatePackageLockJson(newVersion) {
	try {
		if (!fs.existsSync(PROJECT_CONFIG.paths.packageLockJson)) {
			throw new Error("package-lock.json não encontrado.");
		}
		const packageLockJson = JSON.parse(fs.readFileSync(PROJECT_CONFIG.paths.packageLockJson, "utf-8"));
		packageLockJson.version = newVersion;
		fs.writeFileSync(PROJECT_CONFIG.paths.packageLockJson, JSON.stringify(packageLockJson, null, 2), "utf-8");
	} catch (error) {
		throw new Error(`Falha ao atualizar package-lock.json: ${error.message}`);
	}
}

function getUpdatedScriptTsContent(newVersion) {
	try {
		const scriptContent = fs.readFileSync(PROJECT_CONFIG.paths.inputScript, "utf-8");
		const versionRegex = /\/\/\s*@version\s+(?:\[version-number\]|[\d\.]+)/;
		const newVersionLine = `// @version      ${newVersion}`;
		if (!versionRegex.test(scriptContent)) {
			throw new Error("Regex de versão não encontrado no script.ts.");
		}
		const updatedContent = scriptContent.replace(versionRegex, newVersionLine);
		console.log(chalk.blue(`🔍 Versão substituída para: ${newVersion}`));
		return updatedContent;
	} catch (error) {
		throw new Error(`Falha ao atualizar script.ts: ${error.message}`);
	}
}

async function compileTypeScript(outDir, inputPath, options = {}) {
	const { target = "modern", removeComments = false, newVersion = null, optimize = false } = options;

	// Criar configuração TypeScript temporária
	const tempTsConfigPath = path.resolve(outDir, "tsconfig.temp.json");
	const tsConfig = createTsConfig(target, outDir, inputPath);

	if (removeComments) {
		tsConfig.compilerOptions.removeComments = true;
	}

	// Escrever configuração temporária
	fs.writeFileSync(tempTsConfigPath, JSON.stringify(tsConfig, null, 2));

	const targetInfo = PROJECT_CONFIG.compilationTargets[target];
	console.log(chalk.blue(`🎯 Compilando para: ${targetInfo.name}`));
	console.log(chalk.gray(`📋 Recursos: ${targetInfo.features.join(", ")}`));

	return new Promise((resolve, reject) => {
		// Usar apenas o projeto, não especificar arquivo individual
		const command = `npx tsc --project "${tempTsConfigPath}"`;

		exec(command, async (error, stdout, stderr) => {
			// Limpar arquivo temporário
			try {
				fs.unlinkSync(tempTsConfigPath);
			} catch (cleanupError) {
				console.warn(chalk.yellow("⚠️ Não foi possível limpar arquivo temporário"));
			}

			if (error) {
				console.error(chalk.red("❌ Erro ao compilar TypeScript:"));
				console.error(stderr);
				reject(new Error(`Compilação TypeScript falhou: ${stderr}`));
				return;
			}

			if (stdout.trim()) {
				console.log(chalk.gray(stdout));
			}

			try {
				const outputFile = path.resolve(outDir, "script.js");
				await processCompiledFile(outputFile, newVersion, target);

				// Validar compatibilidade
				const compiledContent = fs.readFileSync(outputFile, "utf-8");
				const validation = validateTampermonkeyCompatibility(target, compiledContent);

				if (!validation.compatible) {
					console.log(chalk.yellow("⚠️ Avisos de compatibilidade:"));
					validation.issues.forEach(issue => console.log(chalk.yellow(`   ${issue}`)));
				} else {
					console.log(chalk.green(`✅ Compatível com ${validation.target}`));
				}

				console.log(chalk.green("✅ Cabeçalho UserScript preservado com sucesso."));
			} catch (processingError) {
				console.error(chalk.yellow("⚠️ Aviso: " + processingError.message));
				console.log(chalk.yellow("⚠️ Continuando com o arquivo compilado sem o cabeçalho UserScript."));
			}

			resolve();
		});
	});
}

async function processCompiledFile(filePath, newVersion = null, target = "modern") {
	return new Promise((resolve, reject) => {
		try {
			const compiledContent = fs.readFileSync(filePath, "utf-8");
			const headerRegex = /\/\/ ==UserScript==[\s\S]*?\/\/ ==\/UserScript==/;
			const originalContent = fs.readFileSync(PROJECT_CONFIG.paths.inputScript, "utf-8");
			const headerMatch = originalContent.match(headerRegex);

			if (!headerMatch) {
				throw new Error("Cabeçalho UserScript não encontrado no arquivo original.");
			}

			let userScriptHeader = headerMatch[0];

			// Atualizar versão se fornecida
			if (newVersion) {
				const versionRegex = /\/\/\s*@version\s+(?:\[version-number\]|[\d\.]+)/;
				userScriptHeader = userScriptHeader.replace(versionRegex, `// @version      ${newVersion}`);
			}

			// Adicionar comentário sobre o target de compilação
			const targetInfo = PROJECT_CONFIG.compilationTargets[target];
			const compilationComment = `// Compilado para: ${targetInfo.name} (${targetInfo.description})`;

			const contentWithHeader = compiledContent.replace(headerRegex, "").trim();
			const processedContent = `${userScriptHeader}\n\n${compilationComment}\n\n${contentWithHeader}`;

			fs.writeFileSync(filePath, processedContent, "utf-8");
			resolve(true);
		} catch (error) {
			reject(new Error(`Falha ao processar o arquivo compilado: ${error.message}`));
		}
	});
}

async function main() {
	try {
		await displayBanner();
		await welcomeAnimation();

		// Seleção do tipo de build
		const buildResponse = await prompt({
			type: "select",
			name: "buildType",
			message: "🔧 Qual tipo de build você deseja?",
			choices: [
				{ name: "dev", message: "⚙️   Desenvolvimento (rápido)" },
				{ name: "final", message: "🚀   Produção (completo)" },
				{ name: "test", message: "🧪   Teste (múltiplos targets)" },
			],
			initial: 0,
		});
		const buildType = buildResponse.buildType;

		// Seleção do target de compilação
		const targetChoices = Object.entries(PROJECT_CONFIG.compilationTargets).map(([key, config]) => ({
			name: key,
			message: `${config.name} - ${config.description}`,
		}));

		const targetResponse = await prompt({
			type: "select",
			name: "target",
			message: "🎯 Escolha o target de compilação:",
			choices: targetChoices,
			initial: 0,
		});
		const compilationTarget = targetResponse.target;

		let buildVersionType = null;
		let newVersion = null;
		let versionNotes = null;
		let removeComments = false;
		// Configurações específicas por tipo de build
		if (buildType === "final") {
			const versionResponse = await prompt({
				type: "select",
				name: "versionType",
				message: "🔍 Qual versão você deseja?",
				choices: [
					{ name: "stable", message: "✅ Stable" },
					{ name: "beta", message: "🅱️  Beta" },
				],
				initial: 0,
			});
			buildVersionType = versionResponse.versionType;

			const commentsResponse = await prompt({
				type: "confirm",
				name: "removeComments",
				message: "🧹 Deseja remover comentários do código compilado?",
				initial: true,
			});
			removeComments = commentsResponse.removeComments;

			const notesResponse = await prompt({
				type: "input",
				name: "notes",
				message: "📝 Insira as notas da versão (use '\\n' para novas linhas):",
			});
			versionNotes = notesResponse.notes.replace(/\\n/g, "\n").trim();
			if (!versionNotes) {
				console.log(chalk.yellow("⚠️  Nenhuma nota foi inserida para a versão."));
			}
		} else if (buildType === "test") {
			// Para builds de teste, compilar para todos os targets
			console.log(chalk.blue("🧪 Modo teste: compilando para todos os targets..."));

			for (const [targetKey, targetConfig] of Object.entries(PROJECT_CONFIG.compilationTargets)) {
				console.log(chalk.cyan(`\n📦 Compilando para ${targetConfig.name}...`));

				const testOutDir = path.resolve(PROJECT_CONFIG.paths.distDir, "test", targetKey);
				fs.mkdirSync(testOutDir, { recursive: true });

				try {
					await compileTypeScript(testOutDir, PROJECT_CONFIG.paths.inputScript, {
						target: targetKey,
						removeComments: false,
					});

					const outputFile = path.resolve(testOutDir, "script.js");
					const finalFile = path.resolve(testOutDir, `${PROJECT_CONFIG.scriptName}.${targetKey}.js`);
					fs.renameSync(outputFile, finalFile);

					console.log(chalk.green(`✅ ${targetConfig.name} compilado: ${finalFile}`));
				} catch (error) {
					console.error(chalk.red(`❌ Erro ao compilar ${targetConfig.name}: ${error.message}`));
				}
			}

			console.log(chalk.green("\n🎉 Compilação de teste concluída!"));
			return;
		}
		// Configurar diretórios e arquivos
		let outDir;
		let outputFileName;
		let scriptTsToCompilePath;

		if (buildType === "dev") {
			outDir = PROJECT_CONFIG.paths.distDir;
			outputFileName = `${PROJECT_CONFIG.scriptName}.${compilationTarget}.js`;
			scriptTsToCompilePath = PROJECT_CONFIG.paths.inputScript;
			fs.mkdirSync(outDir, { recursive: true });
		} else {
			// Build de produção
			const packageJson = JSON.parse(fs.readFileSync(PROJECT_CONFIG.paths.packageJson, "utf-8"));
			const currentVersion = packageJson.version || "0.0.0";
			newVersion = incrementVersion(currentVersion);

			updatePackageJson(newVersion);
			updatePackageLockJson(newVersion);

			const updatedScriptContent = getUpdatedScriptTsContent(newVersion);
			outDir = path.resolve(PROJECT_CONFIG.paths.releaseDir, `v${newVersion}`, buildVersionType, compilationTarget);
			outputFileName = `${PROJECT_CONFIG.scriptName}.v${newVersion}.${buildVersionType}.${compilationTarget}.js`;

			fs.mkdirSync(outDir, { recursive: true });

			const versionedScriptTsPath = path.resolve(outDir, "script.ts");
			fs.writeFileSync(versionedScriptTsPath, updatedScriptContent, "utf-8");
			scriptTsToCompilePath = versionedScriptTsPath;

			if (versionNotes) {
				const notesFilePath = path.resolve(outDir, "release_notes.md");
				fs.writeFileSync(notesFilePath, versionNotes, "utf-8");
				console.log(chalk.green(`✅ Notas da versão salvas em: ${notesFilePath}`));
			}
		}
		// Compilação
		const compileSpinner = ora({
			text: `Compilando TypeScript para ${PROJECT_CONFIG.compilationTargets[compilationTarget].name}...`,
			spinner: "circle",
			color: "yellow",
		}).start();

		try {
			if (removeComments) {
				compileSpinner.text = `Compilando TypeScript (${compilationTarget}) e removendo comentários...`;
			}

			await compileTypeScript(outDir, scriptTsToCompilePath, {
				target: compilationTarget,
				removeComments,
				newVersion,
			});

			const targetInfo = PROJECT_CONFIG.compilationTargets[compilationTarget];
			if (removeComments) {
				compileSpinner.succeed(`TypeScript compilado para ${targetInfo.name} com comentários removidos.`);
			} else {
				compileSpinner.succeed(`TypeScript compilado para ${targetInfo.name} com sucesso.`);
			}
		} catch (error) {
			compileSpinner.fail(`Compilação do TypeScript (${compilationTarget}) falhou.`);
			throw error;
		}
		// Renomear arquivo final
		const renameSpinner = ora({
			text: "Finalizando arquivo...",
			spinner: "arrow3",
			color: "magenta",
		}).start();

		const compiledFilePath = path.resolve(outDir, "script.js");
		const finalFilePath = path.resolve(outDir, outputFileName);

		if (!fs.existsSync(compiledFilePath)) {
			renameSpinner.fail("Arquivo compilado não encontrado.");
			throw new Error(`Arquivo compilado não encontrado em ${compiledFilePath}`);
		}

		try {
			fs.renameSync(compiledFilePath, finalFilePath);
			renameSpinner.succeed("Arquivo finalizado com sucesso.");
		} catch (error) {
			renameSpinner.fail("Falha ao finalizar o arquivo.");
			throw new Error(`Erro ao renomear o arquivo: ${error.message}`);
		}

		// Processamentos finais
		const processingSpinner = ora({
			text: "Realizando verificações finais...",
			spinner: "dots",
			color: "green",
		}).start();

		// Verificar tamanho do arquivo
		const stats = fs.statSync(finalFilePath);
		const fileSizeKB = (stats.size / 1024).toFixed(2);

		await new Promise(resolve => setTimeout(resolve, 500));
		processingSpinner.succeed(`Verificações concluídas. Tamanho: ${fileSizeKB}KB`);
		// Mensagens finais
		const targetInfo = PROJECT_CONFIG.compilationTargets[compilationTarget];
		console.log();

		if (buildType === "dev") {
			console.log(chalk.green(`✅ Build de desenvolvimento criado:`));
			console.log(chalk.cyan(`   📁 Arquivo: ${finalFilePath}`));
			console.log(chalk.cyan(`   🎯 Target: ${targetInfo.name}`));
			console.log(chalk.cyan(`   📋 Recursos: ${targetInfo.features.join(", ")}`));
		} else {
			console.log(chalk.green(`✅ Build de produção (${buildVersionType}) criado:`));
			console.log(chalk.cyan(`   📁 Arquivo: ${finalFilePath}`));
			console.log(chalk.cyan(`   🎯 Target: ${targetInfo.name}`));
			console.log(chalk.cyan(`   📋 Recursos: ${targetInfo.features.join(", ")}`));
			console.log(chalk.green(`   🔢 Versão: ${newVersion}`));
		}

		console.log();
		console.log(chalk.blue("💡 Dicas:"));
		console.log(chalk.gray("   • Instale o arquivo .js no Tampermonkey"));
		console.log(chalk.gray("   • Verifique a compatibilidade com sua versão do Tampermonkey"));
		console.log(chalk.gray("   • Use o modo teste para comparar diferentes targets"));

		console.log();
		const finalAnimation = chalkAnimation.rainbow("🎉 Build concluído com sucesso! 🎉");
		await new Promise(resolve => setTimeout(resolve, 2000));
		finalAnimation.stop();
		console.log();
	} catch (error) {
		ora().stop();
		console.error(chalk.red("❌ Ocorreu um erro durante o processo."));
		console.error(chalk.red(`📄 Mensagem: ${error.message}`));
		if (process.env.NODE_ENV === "development") {
			console.error(chalk.red(`🕵️‍♂️ Stack Trace: ${error.stack}`));
		}
		process.exit(1);
	}
}

main().catch(error => {
	console.error(chalk.red("❌ Erro não tratado:"));
	console.error(error);
	process.exit(1);
});
