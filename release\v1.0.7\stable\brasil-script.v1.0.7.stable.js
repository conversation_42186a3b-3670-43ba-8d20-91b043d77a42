// ==UserScript==
// @name         BrasilScripts 🇧🇷
// @namespace    BrasilScripts 🇧🇷
// @version      1.0.7
// <AUTHOR>
// @description  Scripts brasileiros para sigmally
// @match        https://one.sigmally.com
// @icon         https://i.imgur.com/gpwZ3ur.gif
// @grant        none
// ==/UserScript==
(function () {
    "use strict";
    // Chaves de armazenamento no localStorage
    var STORAGE_KEYS = {
        double: "doubleKey",
        triple: "tripleKey",
        tab: "tabKey",
        railgun: "railgunKey",
    };
    // Propriedades padrão para eventos de teclado
    var DEFAULT_KEY_EVENT_PROPS = {
        key: " ",
        code: "Space",
        keyCode: 32,
        which: 32,
        cancelable: true,
        composed: true,
        // isTrusted: true,
    };
    // Propriedades configuráveis para a tecla Tab
    var tabKeyProps = {
        key: "Tab",
        code: "Tab",
    };
    // Estado das macros
    var isMacroDisabled = false;
    var settings = {};
    // Seletores de elementos
    var SELECTORS = {
        navbar: ".mod_menu_navbar",
        menuContent: ".mod_menu_content",
        chatInput: "#chatSendInput",
    };
    // URLs e ícones
    var ICONS = {
        brasil: "https://i.imgur.com/gpwZ3ur.gif",
    };
    // Funções para interação com o localStorage
    var storageService = {
        get: function (key) { return localStorage.getItem(key); },
        set: function (key, value) { return localStorage.setItem(key, value); },
        remove: function (key) { return localStorage.removeItem(key); },
    };
    // Função para mapear 'key' para 'code'
    function mapKeyToCode(key) {
        var keyLower = key === null || key === void 0 ? void 0 : key.toLowerCase();
        var keyUpper = key === null || key === void 0 ? void 0 : key.toUpperCase();
        if (/^[a-zA-Z]$/.test(key))
            return "Key".concat(keyUpper);
        if (/^\d$/.test(key))
            return "Digit".concat(key);
        if (key === " ")
            return "Space";
        var specialKeys = {
            escape: "Escape",
            esc: "Escape",
            tab: "Tab",
            enter: "Enter",
            shift: "ShiftLeft",
            ctrl: "ControlLeft",
            control: "ControlLeft",
            alt: "AltLeft",
            backspace: "Backspace",
            delete: "Delete",
            arrowup: "ArrowUp",
            arrowdown: "ArrowDown",
            arrowleft: "ArrowLeft",
            arrowright: "ArrowRight",
        };
        return specialKeys[keyLower] || keyUpper;
    }
    // Carrega as configurações do localStorage com valores padrão
    function loadSettings() {
        Object.values(STORAGE_KEYS).forEach(function (storageKey) {
            var _a;
            var value = storageService.get(storageKey);
            if (value) {
                settings[storageKey] = (_a = value === null || value === void 0 ? void 0 : value.toLowerCase()) !== null && _a !== void 0 ? _a : undefined;
            }
            else {
                // Define valores padrão
                switch (storageKey) {
                    case STORAGE_KEYS.double:
                        settings[storageKey] = "d"; // Exemplo padrão
                        break;
                    case STORAGE_KEYS.triple:
                        settings[storageKey] = "t"; // Exemplo padrão
                        break;
                    case STORAGE_KEYS.railgun:
                        settings[storageKey] = "r"; // Exemplo padrão
                        break;
                    case STORAGE_KEYS.tab:
                        settings[storageKey] = "tab"; // Valor padrão já existente
                        break;
                }
            }
        });
    }
    // Salva as configurações no localStorage
    function saveSettings() {
        Object.values(STORAGE_KEYS).forEach(function (storageKey) {
            if (settings[storageKey]) {
                storageService.set(storageKey, settings[storageKey]);
            }
            else {
                storageService.remove(storageKey);
            }
        });
        // Atualiza as propriedades da tecla Tab se alterada
        if (settings[STORAGE_KEYS.tab]) {
            tabKeyProps = {
                key: settings[STORAGE_KEYS.tab],
                code: mapKeyToCode(settings[STORAGE_KEYS.tab]),
            };
        }
    }
    // Funções para simular pressionamento de teclas
    function simulateKeyPress(eventProps) {
        window.dispatchEvent(new KeyboardEvent("keydown", eventProps));
        window.dispatchEvent(new KeyboardEvent("keyup", eventProps));
    }
    function triggerSplit(times) {
        if (times <= 0)
            return;
        simulateKeyPress(DEFAULT_KEY_EVENT_PROPS);
        triggerSplit(times - 1);
    }
    function triggerTabPress() {
        simulateKeyPress(tabKeyProps);
    }
    // Criação de elementos de interface
    var createElement = function (tag, options) {
        if (options === void 0) { options = {}; }
        var element = document.createElement(tag);
        if (options.className)
            element.className = options.className;
        if (options.textContent)
            element.textContent = options.textContent;
        if (options.styles)
            Object.assign(element.style, options.styles);
        if (options.attributes) {
            Object.entries(options.attributes).forEach(function (_a) {
                var attr = _a[0], value = _a[1];
                element.setAttribute(attr, value);
            });
        }
        return element;
    };
    // Cria o contêiner principal para os scripts do Brasil
    var brasilScriptsContainer = createElement("div", {
        className: "brasil-scripts-container mod_tab scroll",
        styles: { display: "none" },
    });
    // Funções para criar elementos de UI
    function createTitle(text) {
        var title = createElement("div", { className: "text-center", textContent: text });
        brasilScriptsContainer.appendChild(title);
        return title;
    }
    function createCategory(_a) {
        var title = _a.title, emoji = _a.emoji;
        var categoryContainer = createElement("div", { styles: { display: "flex", alignItems: "center", margin: "10px 0" } });
        var emojiSpan = createElement("span", { textContent: emoji, styles: { marginRight: "8px", fontSize: "1.2em" } });
        var titleSpan = createElement("span", { textContent: title, styles: { fontWeight: "bold", marginRight: "8px" } });
        var line = createElement("div", { styles: { flexGrow: "1", height: "1px", backgroundColor: "#bfbfbf", marginLeft: "8px" } });
        categoryContainer.appendChild(emojiSpan);
        categoryContainer.appendChild(titleSpan);
        categoryContainer.appendChild(line);
        brasilScriptsContainer.appendChild(categoryContainer);
        return categoryContainer;
    }
    function createKeybindInput(_a) {
        var _b;
        var idPrefix = _a.idPrefix, property = _a.property, title = _a.title, helpText = _a.helpText, _c = _a.container, container = _c === void 0 ? brasilScriptsContainer : _c;
        var inputContainer = createElement("div", {
            className: "keybind-input-container modRowItems justify-sb",
            styles: { position: "relative", padding: "5px 10px" },
        });
        inputContainer.title = helpText;
        var label = createElement("span", { textContent: title });
        var input = createElement("input", {
            className: "keybind-input modInput",
            styles: { display: "flex", justifyContent: "center", textAlign: "center", alignItems: "center" },
            attributes: {
                type: "text",
                id: "".concat(idPrefix, "-").concat(property),
                placeholder: "...",
            },
        });
        input.style.width = idPrefix === "sf" ? "40px" : "50px";
        input.value = property === STORAGE_KEYS.tab ? "Tab" : ((_b = settings[property]) === null || _b === void 0 ? void 0 : _b.toUpperCase()) || "";
        // Evento para capturar a tecla pressionada
        input.addEventListener("keydown", function (e) {
            e.preventDefault();
            var key = e.key;
            var keyLower = key.toLowerCase();
            if (key === "Escape" || key === "Backspace") {
                settings[property] = "";
                input.value = property === STORAGE_KEYS.tab ? "Tab" : "";
                saveSettings();
                updateKeyHandlers(); // Atualiza os handlers após a mudança
                return;
            }
            // Verifica conflitos de teclas
            var isConflict = Object.values(STORAGE_KEYS).some(function (keyItem) { var _a; return keyItem !== property && ((_a = settings[keyItem]) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === keyLower; });
            if (isConflict) {
                alert("A tecla \"".concat(key, "\" j\u00E1 est\u00E1 atribu\u00EDda a outro comando."));
                return;
            }
            settings[property] = keyLower;
            input.value = key.toUpperCase(); // Mostra a tecla com a formatação original
            saveSettings();
            updateKeyHandlers(); // Atualiza os handlers após a mudança
        });
        inputContainer.appendChild(label);
        inputContainer.appendChild(input);
        container.appendChild(inputContainer);
    }
    // Cria o botão de navegação
    function createNavigationButton(navigationMenu, menuContent) {
        var navButton = createElement("button", { className: "brasil-nav-btn mod_nav_btn" });
        var img = createElement("img", {
            styles: {
                width: "20px",
                height: "20px",
                verticalAlign: "middle",
                borderRadius: "50%",
            },
            attributes: {
                src: ICONS.brasil,
                alt: "Ícone Brasil",
            },
        });
        var buttonText = createElement("span", { textContent: " Brasil" });
        navButton.appendChild(img);
        navButton.appendChild(buttonText);
        navigationMenu.appendChild(navButton);
        // Evento de clique para mostrar o contêiner de scripts do Brasil
        navButton.addEventListener("click", function () {
            // Oculta todas as abas existentes
            document.querySelectorAll(".mod_tab").forEach(function (tab) {
                var tabElement = tab;
                tabElement.style.opacity = "0";
                setTimeout(function () {
                    tabElement.style.display = "none";
                }, 200);
            });
            // Remove a classe 'mod_selected' de todos os botões de navegação
            document.querySelectorAll(".mod_nav_btn").forEach(function (btn) {
                btn.classList.remove("mod_selected");
            });
            // Adiciona a classe 'mod_selected' ao botão clicado
            navButton.classList.add("mod_selected");
            // Exibe o contêiner 'brasilScriptsContainer' com uma transição suave
            setTimeout(function () {
                brasilScriptsContainer.style.display = "flex";
                setTimeout(function () {
                    brasilScriptsContainer.style.opacity = "1";
                }, 10);
            }, 200);
        });
    }
    // Cria o contêiner de configuração
    function createConfigContainer() {
        var navigationMenu = document.querySelector(SELECTORS.navbar);
        var menuContent = document.querySelector(SELECTORS.menuContent);
        if (!navigationMenu || !menuContent)
            return;
        navigationMenu.style.gap = "8px";
        // Cria e adiciona o botão de navegação
        createNavigationButton(navigationMenu, menuContent);
        // Adiciona o contêiner de scripts do Brasil ao conteúdo do menu
        menuContent.appendChild(brasilScriptsContainer);
        // Cria os elementos de título e categorias
        createTitle("Brasil 🇧🇷 Scripts");
        createCategory({ title: "Teclas Padrões", emoji: "⚙️" });
        // Configura os keybinds
        configureKeybinds(brasilScriptsContainer);
    }
    // Configuração dos keybinds
    function configureKeybinds(container) {
        createKeybindInput({
            idPrefix: "brasil",
            property: STORAGE_KEYS.tab,
            title: "Tecla para trocar de aba:",
            helpText: "Tecla para realizar o Tab padrão",
            container: container,
        });
        createCategory({ title: "InfernoSplit", emoji: "🔥" });
        createKeybindInput({
            idPrefix: "brasil",
            property: STORAGE_KEYS.double,
            title: "Tecla para Double (2):",
            helpText: "Tecla para realizar o Double Split",
            container: container,
        });
        createKeybindInput({
            idPrefix: "brasil",
            property: STORAGE_KEYS.triple,
            title: "Tecla para Triple (3):",
            helpText: "Tecla para realizar o Triple Split",
            container: container,
        });
        createCategory({ title: "Railgun", emoji: "☄️" });
        createKeybindInput({
            idPrefix: "brasil",
            property: STORAGE_KEYS.railgun,
            title: "Tecla para Railgun (3):",
            helpText: "Tecla para realizar o Triple Reto",
            container: container,
        });
    }
    // Handlers específicos
    function handleDoubleHellKey(event) {
        event.preventDefault();
        triggerSplit(2);
        triggerTabPress();
        triggerSplit(4);
        setTimeout(function () {
            triggerTabPress();
        }, 0);
    }
    function handleTripleHellKey(event) {
        event.preventDefault();
        triggerSplit(3);
        triggerTabPress();
        triggerSplit(4);
        setTimeout(function () {
            triggerTabPress();
        }, 0);
    }
    function handleRailgunKey(event) {
        event.preventDefault();
        var originalMove;
        var restoreTimeout;
        // Salva a função original e a desativa
        originalMove = sigfix.net.move;
        sigfix.net.move = function () { };
        triggerSplit(3);
        // Restaura a função original após 750ms
        restoreTimeout = setTimeout(function () {
            if (originalMove) {
                sigfix.net.move = originalMove;
            }
            originalMove = undefined;
            restoreTimeout = undefined;
        }, 750);
    }
    // Função para atualizar os keyHandlers após as configurações mudarem
    var keyHandlers = {};
    function updateKeyHandlers() {
        var _a;
        var _b, _c, _d;
        keyHandlers = (_a = {},
            _a[(_b = settings[STORAGE_KEYS.double]) === null || _b === void 0 ? void 0 : _b.toLowerCase()] = handleDoubleHellKey,
            _a[(_c = settings[STORAGE_KEYS.triple]) === null || _c === void 0 ? void 0 : _c.toLowerCase()] = handleTripleHellKey,
            _a[(_d = settings[STORAGE_KEYS.railgun]) === null || _d === void 0 ? void 0 : _d.toLowerCase()] = handleRailgunKey,
            _a);
    }
    // Inicialização dos keyHandlers após as configurações serem carregadas
    function initializeKeyHandlers() {
        updateKeyHandlers();
    }
    // Manipulador de eventos para keydown global
    function handleGlobalKeydown(event) {
        var _a;
        if (isMacroDisabled)
            return;
        var pressedKey = (_a = event.key) === null || _a === void 0 ? void 0 : _a.toLowerCase();
        var keyHandler = keyHandlers[pressedKey];
        if (keyHandler) {
            keyHandler(event);
        }
    }
    // Função para esperar elementos na DOM
    function waitForElements(selectors, callback, intervalTime, maxAttempts) {
        if (intervalTime === void 0) { intervalTime = 500; }
        if (maxAttempts === void 0) { maxAttempts = 20; }
        var attempts = 0;
        var interval = setInterval(function () {
            var allPresent = selectors.every(function (selector) { return document.querySelector(selector); });
            if (allPresent) {
                clearInterval(interval);
                callback();
            }
            else {
                attempts++;
                if (attempts >= maxAttempts) {
                    clearInterval(interval);
                    console.warn("waitForElements: Elementos n\u00E3o encontrados ap\u00F3s ".concat(maxAttempts, " tentativas."));
                }
            }
        }, intervalTime);
    }
    // Desativa macros quando o input de chat está focado
    function monitorChatInput() {
        var interval = setInterval(function () {
            var chatInput = document.querySelector(SELECTORS.chatInput);
            if (chatInput) {
                clearInterval(interval);
                var disableIndicator_1 = createElement("div", {
                    styles: {
                        display: "none",
                        position: "fixed",
                        bottom: "10px",
                        right: "10px",
                        padding: "5px 10px",
                        backgroundColor: "rgba(0,0,0,0.7)",
                        color: "#fff",
                        borderRadius: "5px",
                        zIndex: "1000",
                    },
                });
                disableIndicator_1.id = "brasil-disable-indicator";
                disableIndicator_1.textContent = "Desativado durante o chat";
                document.body.appendChild(disableIndicator_1);
                chatInput.addEventListener("focus", function () {
                    isMacroDisabled = true;
                    disableIndicator_1.style.display = "block";
                });
                chatInput.addEventListener("blur", function () {
                    isMacroDisabled = false;
                    disableIndicator_1.style.display = "none";
                });
            }
        }, 500);
    }
    // Função de inicialização
    function initialize() {
        loadSettings();
        initializeKeyHandlers();
        waitForElements([SELECTORS.navbar, SELECTORS.menuContent], createConfigContainer);
        document.addEventListener("keydown", handleGlobalKeydown);
        monitorChatInput();
        console.log("BrasilScripts carregado com sucesso.");
    }
    // Inicia o script
    initialize();
})();
