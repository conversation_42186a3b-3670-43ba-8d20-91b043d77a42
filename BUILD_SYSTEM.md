# 🚀 Sistema de Build Moderno para Tampermonkey

## 📋 Visão Geral

Este é um sistema de build avançado e extensível para scripts Tampermonkey, desenvolvido para suportar diferentes versões do Tampermonkey e oferecer uma experiência de desenvolvimento moderna.

## ✨ Funcionalidades

### 🎯 Múltiplos Targets de Compilação

- **Moderno (ES2020+)**: Para Tampermonkey 4.14+ com suporte completo a recursos modernos
  - `const` e `let`
  - Arrow functions
  - Template literals
  - Destructuring
  - Classes modernas

- **Compatível (ES2015)**: Para Tampermonkey 4.0+ com boa compatibilidade
  - `const` e `let`
  - Arrow functions
  - Template literals
  - Compatibilidade ampla

- **Legado (ES5)**: Para versões antigas do Tampermonkey
  - Apenas `var`
  - Function expressions
  - Máxima compatibilidade

### 🔧 Tipos de Build

1. **Desenvolvimento**: Build rápido para testes
2. **Produção**: Build completo com versionamento
3. **Teste**: Compila para todos os targets simultaneamente

### 🛡️ Validação e Compatibilidade

- Verificação automática de compatibilidade
- Detecção de recursos não suportados
- Validação de headers UserScript
- Análise de tamanho de arquivo

## 🚀 Como Usar

### Executar o Build

```bash
npm run build:config
```

### Interface Interativa

O sistema oferece uma interface CLI moderna com:

1. **Seleção do tipo de build**
2. **Escolha do target de compilação**
3. **Configurações específicas** (comentários, notas de versão)
4. **Feedback visual** com spinners e animações

### Estrutura de Saída

#### Desenvolvimento
```
dist/
├── brasil-script.modern.js
├── brasil-script.compatible.js
└── brasil-script.legacy.js
```

#### Produção
```
release/
└── v1.0.x/
    └── stable/
        ├── modern/
        │   ├── brasil-script.v1.0.x.stable.modern.js
        │   ├── script.ts
        │   └── release_notes.md
        ├── compatible/
        └── legacy/
```

#### Teste
```
dist/
└── test/
    ├── modern/
    ├── compatible/
    └── legacy/
```

## 🔧 Configuração

### Targets de Compilação

Os targets são configurados em `PROJECT_CONFIG.compilationTargets`:

```javascript
compilationTargets: {
  modern: {
    name: "Moderno (ES2020+)",
    description: "Para Tampermonkey 4.14+",
    tsConfig: {
      target: "ES2020",
      lib: ["DOM", "ES2020"]
    },
    features: ["const", "let", "arrow-functions"]
  }
}
```

### TypeScript

O sistema gera configurações TypeScript dinamicamente baseadas no target selecionado, garantindo compatibilidade otimizada.

## 📁 Estrutura do Projeto

```
brasil-scripts/
├── src/
│   ├── script.ts          # Script principal
│   └── types/             # Definições de tipos
├── dist/                  # Builds de desenvolvimento
├── release/               # Builds de produção
├── build-config.mjs       # Sistema de build
├── tsconfig.json          # Config TypeScript padrão
└── package.json
```

## 🎨 Recursos Visuais

- 🇧🇷 Banner brasileiro personalizado
- 🌈 Animações coloridas
- ⚡ Spinners informativos
- 📊 Relatórios de compatibilidade
- 💡 Dicas e sugestões

## 🔍 Validações

O sistema verifica automaticamente:

- ✅ Compatibilidade com o target selecionado
- ✅ Headers UserScript válidos
- ✅ Conflitos de permissões @grant
- ✅ Uso de recursos não suportados
- ✅ Tamanho do arquivo final

## 📝 Versionamento

- Incremento automático de versão
- Atualização de package.json e package-lock.json
- Preservação de headers UserScript
- Notas de versão opcionais

## 🚀 Próximos Passos

- [ ] Templates para diferentes tipos de scripts
- [ ] Otimização automática de código
- [ ] Integração com CI/CD
- [ ] Análise de dependências
- [ ] Hot reload para desenvolvimento
