// ==UserScript==
// @name         BrasilScripts 🇧🇷
// @namespace    BrasilScripts 🇧🇷
// @version      1.0.2
// author        kevinkvn_
// @description  Brasil 🇧🇷 Scripts para sigmally
// @match        https://one.sigmally.com
// @icon         https://i.imgur.com/gpwZ3ur.gif
// @grant        none
// ==/UserScript==
(function () {
    "use strict";
    var STORAGE_KEYS = {
        double: "doubleKey",
        triple: "tripleKey",
    };
    var KEY_SPLIT_PROPS = {
        key: " ",
        code: "Space",
        keyCode: 32,
        which: 32,
        cancelable: true,
        composed: true,
        isTrusted: true,
    };
    var TAB_KEY_PROPS = {
        key: "Tab",
        code: "Tab",
    };
    var isMacroDisabled = false;
    function getLocalStorageKey(key) {
        return localStorage.getItem(key);
    }
    function setLocalStorageKey(key, value) {
        localStorage.setItem(key, value);
    }
    function removeLocalStorageKey(key) {
        localStorage.removeItem(key);
    }
    function split(times) {
        if (times <= 0)
            return;
        window.dispatchEvent(new KeyboardEvent("keydown", KEY_SPLIT_PROPS));
        window.dispatchEvent(new KeyboardEvent("keyup", KEY_SPLIT_PROPS));
        split(times - 1);
    }
    function pressTab() {
        window.dispatchEvent(new KeyboardEvent("keydown", TAB_KEY_PROPS));
        window.dispatchEvent(new KeyboardEvent("keyup", TAB_KEY_PROPS));
    }
    function injectStyles() {
        var style = document.createElement("style");
        style.textContent = "\n\t\t@keyframes neon-glow {\n\t\t  0%   { box-shadow: 0 0 6px #01ff70; }\n\t\t  100% { box-shadow: 0 0 15px #01ff70; }\n\t\t}\n  \n\t\t#inferno-config-minimalista {\n\t\t  z-index: 9999999;\n\t\t  position: absolute;\n\t\t  top: 20px; left: 20px;\n\t\t  width: 240px;\n\t\t  padding: 15px;\n\t\t  display: flex;\n\t\t  flex-direction: column;\n\t\t  align-items: center;\n\t\t  justify-content: center;\n  \n\t\t  background: linear-gradient(135deg, #151515, #1A0E2D);\n\t\t  color: #ffffff;\n\t\t  border: 2px solid #01ff70;\n\t\t  border-radius: 10px;\n\t\t  font-family: Arial, sans-serif;\n\t\t  box-sizing: border-box;\n\t\t  animation: neon-glow 2s ease-in-out infinite alternate;\n\t\t  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);\n\t\t}\n  \n\t\t#inferno-config-minimalista h3 {\n\t\t  margin: 0 0 12px;\n\t\t  font-size: 20px;\n\t\t  text-align: center;\n\t\t  color: rgba(1, 255, 112, 0.8);\n\t\t  text-shadow: 0 0 6px #01ff70;\n\t\t}\n  \n\t\t#inferno-config-minimalista p {\n\t\t  margin: 0 0 8px;\n\t\t  font-size: 14px;\n\t\t  line-height: 1.4;\n\t\t  color: #f8f8f2;\n\t\t  text-align: center;\n\t\t}\n  \n\t\t.campo-config {\n\t\t  display: flex;\n\t\t  flex-direction: column;\n\t\t  align-items: center;\n\t\t  margin-bottom: 12px;\n\t\t}\n  \n\t\t#inferno-config-minimalista input {\n\t\t  width: 120px;\n\t\t  max-width: 120px;\n\t\t  padding: 6px;\n\t\t  border: 1px solid #01ff70;\n\t\t  border-radius: 4px;\n\t\t  background-color: rgba(0, 0, 0, 0.3);\n\t\t  color: #fff;\n\t\t  outline: none;\n\t\t  text-align: center;\n\t\t  transition: all 0.2s ease;\n  \n\t\t  white-space: nowrap;\n\t\t  overflow: hidden;\n\t\t  text-overflow: ellipsis;\n\t\t}\n  \n\t\t#inferno-config-minimalista input:hover {\n\t\t  background-color: rgba(0, 0, 0, 0.5);\n\t\t}\n  \n\t\t#inferno-config-minimalista input:focus {\n\t\t  box-shadow: 0 0 8px #01ff70;\n\t\t  background-color: rgba(0, 0, 0, 0.6);\n\t\t}\n  \n\t\t#inferno-sign {\n\t\t  position: absolute;\n\t\t  bottom: 4px;\n\t\t  right: 6px;\n\t\t  font-size: 10px;\n\t\t  color: #ffffff90;\n\t\t  font-family: Arial, sans-serif;\n\t\t  pointer-events: none;\n\t\t}\n\n\t\t        #inferno-disable-indicator {\n          position: absolute;\n          top: 15px;\n          right: 15px;\n          color: #fff;\n          background-color: rgba(255, 0, 0, 0.8);\n          padding: 6px 10px;\n          border: 1px solid #ff5757;\n          border-radius: 4px;\n          font-size: 12px;\n          font-family: Arial, sans-serif;\n          display: none;\n          z-index: 99999999;\n          text-transform: uppercase;\n          box-shadow: 0 0 6px rgba(255, 0, 0, 0.5);\n        }\n\t  ";
        document.head.appendChild(style);
    }
    function createConfigContainer() {
        var overlays = document.getElementById("overlays");
        if (!overlays)
            return;
        if (document.getElementById("inferno-config-minimalista"))
            return;
        injectStyles();
        var container = document.createElement("div");
        container.id = "inferno-config-minimalista";
        var title = document.createElement("h3");
        title.textContent = "InfernoSplit";
        container.appendChild(title);
        function createConfigField(labelText, placeholder, storageKey) {
            var fieldWrapper = document.createElement("div");
            fieldWrapper.className = "campo-config";
            var label = document.createElement("p");
            label.textContent = labelText;
            var input = document.createElement("input");
            input.type = "text";
            input.placeholder = placeholder;
            var savedKey = getLocalStorageKey(storageKey);
            if (savedKey) {
                input.value = savedKey;
            }
            input.addEventListener("keydown", function (e) {
                e.preventDefault();
                var pressedKey = e.key;
                var otherKey = storageKey === STORAGE_KEYS.double ? getLocalStorageKey(STORAGE_KEYS.triple) : getLocalStorageKey(STORAGE_KEYS.double);
                if (otherKey && otherKey === pressedKey) {
                    var keyToRemove = storageKey === STORAGE_KEYS.double ? STORAGE_KEYS.triple : STORAGE_KEYS.double;
                    removeLocalStorageKey(keyToRemove);
                    var otherInput = container.querySelectorAll("input")[storageKey === STORAGE_KEYS.double ? 1 : 0];
                    if (otherInput) {
                        otherInput.value = "";
                    }
                }
                setLocalStorageKey(storageKey, pressedKey);
                input.value = pressedKey;
            });
            fieldWrapper.appendChild(label);
            fieldWrapper.appendChild(input);
            container.appendChild(fieldWrapper);
            return input;
        }
        createConfigField("Tecla para Double (2):", "Double Key...", STORAGE_KEYS.double);
        createConfigField("Tecla para Triple (3):", "Triple Key...", STORAGE_KEYS.triple);
        var sign = document.createElement("div");
        sign.id = "inferno-sign";
        sign.textContent = "by kevinkvn_";
        container.appendChild(sign);
        overlays.appendChild(container);
        var disableIndicator = document.createElement("div");
        disableIndicator.id = "inferno-disable-indicator";
        disableIndicator.textContent = "Desativado durante o chat";
        document.body.appendChild(disableIndicator);
    }
    function handleGlobalKeydown(e) {
        if (isMacroDisabled)
            return;
        var doubleKey = getLocalStorageKey(STORAGE_KEYS.double);
        var tripleKey = getLocalStorageKey(STORAGE_KEYS.triple);
        if (!doubleKey && !tripleKey)
            return;
        if (e.key === doubleKey) {
            e.preventDefault();
            split(2);
            pressTab();
            split(4);
        }
        if (e.key === tripleKey) {
            e.preventDefault();
            split(3);
            pressTab();
            split(4);
        }
    }
    function waitForChatInputAndBind() {
        var interval = setInterval(function () {
            var chatSendInput = document.getElementById("chatSendInput");
            if (chatSendInput) {
                clearInterval(interval);
                var disableIndicator_1 = document.getElementById("inferno-disable-indicator");
                if (disableIndicator_1) {
                    chatSendInput.addEventListener("focus", function () {
                        isMacroDisabled = true;
                        disableIndicator_1.style.display = "block";
                    });
                    chatSendInput.addEventListener("blur", function () {
                        isMacroDisabled = false;
                        disableIndicator_1.style.display = "none";
                    });
                }
            }
        }, 500);
    }
    function init() {
        createConfigContainer();
        document.addEventListener("keydown", handleGlobalKeydown);
        waitForChatInputAndBind();
        console.log("InfernoScript com double/triple e exclusividade de teclas - Loaded.");
    }
    init();
})();
