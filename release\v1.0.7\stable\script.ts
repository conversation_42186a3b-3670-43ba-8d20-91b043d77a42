// ==UserScript==
// @name         BrasilScripts 🇧🇷
// @namespace    BrasilScripts 🇧🇷
// @version      1.0.7
// <AUTHOR>
// @description  Scripts brasileiros para sigmally
// @match        https://one.sigmally.com
// @icon         https://i.imgur.com/gpwZ3ur.gif
// @grant        none
// ==/UserScript==

declare namespace sigfix {
	namespace net {
		function move(): void;
		// Adicione outras funções ou propriedades conforme necessário
	}
}

(function () {
	"use strict";

	// Chaves de armazenamento no localStorage
	const STORAGE_KEYS = {
		double: "doubleKey",
		triple: "tripleKey",
		tab: "tabKey",
		railgun: "railgunKey",
	} as const;

	type LocalStorageKey = (typeof STORAGE_KEYS)[keyof typeof STORAGE_KEYS];
	type KeyHandler = (event: KeyboardEvent) => void;

	// Propriedades padrão para eventos de teclado
	const DEFAULT_KEY_EVENT_PROPS: KeyboardEventInit = {
		key: " ",
		code: "Space",
		keyCode: 32,
		which: 32,
		cancelable: true,
		composed: true,
		// isTrusted: true,
	};

	// Propriedades configuráveis para a tecla Tab
	let tabKeyProps = {
		key: "Tab",
		code: "Tab",
	};

	// Estado das macros
	let isMacroDisabled = false;
	const settings: { [key: string]: string } = {};

	// Seletores de elementos
	const SELECTORS = {
		navbar: ".mod_menu_navbar",
		menuContent: ".mod_menu_content",
		chatInput: "#chatSendInput",
	};

	// URLs e ícones
	const ICONS = {
		brasil: "https://i.imgur.com/gpwZ3ur.gif",
	};

	// Funções para interação com o localStorage
	const storageService = {
		get: (key: LocalStorageKey): string | null => localStorage.getItem(key),
		set: (key: LocalStorageKey, value: string): void => localStorage.setItem(key, value),
		remove: (key: LocalStorageKey): void => localStorage.removeItem(key),
	};

	// Função para mapear 'key' para 'code'
	function mapKeyToCode(key: string): string {
		const keyLower = key?.toLowerCase();
		const keyUpper = key?.toUpperCase();

		if (/^[a-zA-Z]$/.test(key)) return `Key${keyUpper}`;
		if (/^\d$/.test(key)) return `Digit${key}`;
		if (key === " ") return "Space";

		const specialKeys: { [key: string]: string } = {
			escape: "Escape",
			esc: "Escape",
			tab: "Tab",
			enter: "Enter",
			shift: "ShiftLeft",
			ctrl: "ControlLeft",
			control: "ControlLeft",
			alt: "AltLeft",
			backspace: "Backspace",
			delete: "Delete",
			arrowup: "ArrowUp",
			arrowdown: "ArrowDown",
			arrowleft: "ArrowLeft",
			arrowright: "ArrowRight",
		};

		return specialKeys[keyLower] || keyUpper;
	}

	// Carrega as configurações do localStorage com valores padrão
	function loadSettings(): void {
		Object.values(STORAGE_KEYS).forEach(storageKey => {
			const value = storageService.get(storageKey);
			if (value) {
				settings[storageKey] = value?.toLowerCase() ?? undefined;
			} else {
				// Define valores padrão
				switch (storageKey) {
					case STORAGE_KEYS.double:
						settings[storageKey] = "d"; // Exemplo padrão
						break;
					case STORAGE_KEYS.triple:
						settings[storageKey] = "t"; // Exemplo padrão
						break;
					case STORAGE_KEYS.railgun:
						settings[storageKey] = "r"; // Exemplo padrão
						break;
					case STORAGE_KEYS.tab:
						settings[storageKey] = "tab"; // Valor padrão já existente
						break;
				}
			}
		});
	}

	// Salva as configurações no localStorage
	function saveSettings(): void {
		Object.values(STORAGE_KEYS).forEach(storageKey => {
			if (settings[storageKey]) {
				storageService.set(storageKey, settings[storageKey]);
			} else {
				storageService.remove(storageKey);
			}
		});

		// Atualiza as propriedades da tecla Tab se alterada
		if (settings[STORAGE_KEYS.tab]) {
			tabKeyProps = {
				key: settings[STORAGE_KEYS.tab],
				code: mapKeyToCode(settings[STORAGE_KEYS.tab]),
			};
		}
	}

	// Funções para simular pressionamento de teclas
	function simulateKeyPress(eventProps: KeyboardEventInit): void {
		window.dispatchEvent(new KeyboardEvent("keydown", eventProps));
		window.dispatchEvent(new KeyboardEvent("keyup", eventProps));
	}

	function triggerSplit(times: number): void {
		if (times <= 0) return;
		simulateKeyPress(DEFAULT_KEY_EVENT_PROPS);
		triggerSplit(times - 1);
	}

	function triggerTabPress(): void {
		simulateKeyPress(tabKeyProps);
	}

	// Criação de elementos de interface
	const createElement = (
		tag: string,
		options: {
			className?: string;
			textContent?: string;
			styles?: Partial<CSSStyleDeclaration>;
			attributes?: { [key: string]: string };
		} = {}
	): HTMLElement => {
		const element = document.createElement(tag);
		if (options.className) element.className = options.className;
		if (options.textContent) element.textContent = options.textContent;
		if (options.styles) Object.assign(element.style, options.styles);
		if (options.attributes) {
			Object.entries(options.attributes).forEach(([attr, value]) => {
				element.setAttribute(attr, value);
			});
		}
		return element;
	};

	// Cria o contêiner principal para os scripts do Brasil
	const brasilScriptsContainer = createElement("div", {
		className: "brasil-scripts-container mod_tab scroll",
		styles: { display: "none" },
	});

	// Funções para criar elementos de UI
	function createTitle(text: string): HTMLElement {
		const title = createElement("div", { className: "text-center", textContent: text });
		brasilScriptsContainer.appendChild(title);
		return title;
	}

	function createCategory({ title, emoji }: { title: string; emoji: string }): HTMLElement {
		const categoryContainer = createElement("div", { styles: { display: "flex", alignItems: "center", margin: "10px 0" } });

		const emojiSpan = createElement("span", { textContent: emoji, styles: { marginRight: "8px", fontSize: "1.2em" } });
		const titleSpan = createElement("span", { textContent: title, styles: { fontWeight: "bold", marginRight: "8px" } });
		const line = createElement("div", { styles: { flexGrow: "1", height: "1px", backgroundColor: "#bfbfbf", marginLeft: "8px" } });

		categoryContainer.appendChild(emojiSpan);
		categoryContainer.appendChild(titleSpan);
		categoryContainer.appendChild(line);
		brasilScriptsContainer.appendChild(categoryContainer);

		return categoryContainer;
	}

	function createKeybindInput({
		idPrefix,
		property,
		title,
		helpText,
		container = brasilScriptsContainer,
	}: {
		idPrefix: string;
		property: string;
		title: string;
		helpText: string;
		container?: HTMLElement;
	}): void {
		const inputContainer = createElement("div", {
			className: "keybind-input-container modRowItems justify-sb",
			styles: { position: "relative", padding: "5px 10px" },
		});
		inputContainer.title = helpText;

		const label = createElement("span", { textContent: title });

		const input = createElement("input", {
			className: "keybind-input modInput",
			styles: { display: "flex", justifyContent: "center", textAlign: "center", alignItems: "center" },
			attributes: {
				type: "text",
				id: `${idPrefix}-${property}`,
				placeholder: "...",
			},
		}) as HTMLInputElement;
		input.style.width = idPrefix === "sf" ? "40px" : "50px";
		input.value = property === STORAGE_KEYS.tab ? "Tab" : settings[property]?.toUpperCase() || "";

		// Evento para capturar a tecla pressionada
		input.addEventListener("keydown", e => {
			e.preventDefault();

			const key = e.key;
			const keyLower = key.toLowerCase();

			if (key === "Escape" || key === "Backspace") {
				settings[property] = "";
				input.value = property === STORAGE_KEYS.tab ? "Tab" : "";
				saveSettings();
				updateKeyHandlers(); // Atualiza os handlers após a mudança
				return;
			}

			// Verifica conflitos de teclas
			const isConflict = Object.values(STORAGE_KEYS).some(keyItem => keyItem !== property && settings[keyItem]?.toLowerCase() === keyLower);

			if (isConflict) {
				alert(`A tecla "${key}" já está atribuída a outro comando.`);
				return;
			}

			settings[property] = keyLower;
			input.value = key.toUpperCase(); // Mostra a tecla com a formatação original
			saveSettings();
			updateKeyHandlers(); // Atualiza os handlers após a mudança
		});

		inputContainer.appendChild(label);
		inputContainer.appendChild(input);
		container.appendChild(inputContainer);
	}

	// Cria o botão de navegação
	function createNavigationButton(navigationMenu: HTMLElement, menuContent: HTMLElement): void {
		const navButton = createElement("button", { className: "brasil-nav-btn mod_nav_btn" });

		const img = createElement("img", {
			styles: {
				width: "20px",
				height: "20px",
				verticalAlign: "middle",
				borderRadius: "50%",
			},
			attributes: {
				src: ICONS.brasil,
				alt: "Ícone Brasil",
			},
		}) as HTMLImageElement;

		const buttonText = createElement("span", { textContent: " Brasil" });

		navButton.appendChild(img);
		navButton.appendChild(buttonText);
		navigationMenu.appendChild(navButton);

		// Evento de clique para mostrar o contêiner de scripts do Brasil
		navButton.addEventListener("click", () => {
			// Oculta todas as abas existentes
			document.querySelectorAll(".mod_tab").forEach(tab => {
				const tabElement = tab as HTMLElement;
				tabElement.style.opacity = "0";
				setTimeout(() => {
					tabElement.style.display = "none";
				}, 200);
			});

			// Remove a classe 'mod_selected' de todos os botões de navegação
			document.querySelectorAll(".mod_nav_btn").forEach(btn => {
				btn.classList.remove("mod_selected");
			});

			// Adiciona a classe 'mod_selected' ao botão clicado
			navButton.classList.add("mod_selected");

			// Exibe o contêiner 'brasilScriptsContainer' com uma transição suave
			setTimeout(() => {
				brasilScriptsContainer.style.display = "flex";
				setTimeout(() => {
					brasilScriptsContainer.style.opacity = "1";
				}, 10);
			}, 200);
		});
	}

	// Cria o contêiner de configuração
	function createConfigContainer(): void {
		const navigationMenu = document.querySelector(SELECTORS.navbar) as HTMLElement | null;
		const menuContent = document.querySelector(SELECTORS.menuContent) as HTMLElement | null;

		if (!navigationMenu || !menuContent) return;
		navigationMenu.style.gap = "8px";

		// Cria e adiciona o botão de navegação
		createNavigationButton(navigationMenu, menuContent);

		// Adiciona o contêiner de scripts do Brasil ao conteúdo do menu
		menuContent.appendChild(brasilScriptsContainer);

		// Cria os elementos de título e categorias
		createTitle("Brasil 🇧🇷 Scripts");
		createCategory({ title: "Teclas Padrões", emoji: "⚙️" });

		// Configura os keybinds
		configureKeybinds(brasilScriptsContainer);
	}

	// Configuração dos keybinds
	function configureKeybinds(container: HTMLElement): void {
		createKeybindInput({
			idPrefix: "brasil",
			property: STORAGE_KEYS.tab,
			title: "Tecla para trocar de aba:",
			helpText: "Tecla para realizar o Tab padrão",
			container,
		});

		createCategory({ title: "InfernoSplit", emoji: "🔥" });

		createKeybindInput({
			idPrefix: "brasil",
			property: STORAGE_KEYS.double,
			title: "Tecla para Double (2):",
			helpText: "Tecla para realizar o Double Split",
			container,
		});

		createKeybindInput({
			idPrefix: "brasil",
			property: STORAGE_KEYS.triple,
			title: "Tecla para Triple (3):",
			helpText: "Tecla para realizar o Triple Split",
			container,
		});

		createCategory({ title: "Railgun", emoji: "☄️" });
		createKeybindInput({
			idPrefix: "brasil",
			property: STORAGE_KEYS.railgun,
			title: "Tecla para Railgun (3):",
			helpText: "Tecla para realizar o Triple Reto",
			container: container,
		});
	}

	// Handlers específicos
	function handleDoubleHellKey(event: KeyboardEvent): void {
		event.preventDefault();
		triggerSplit(2);
		triggerTabPress();
		triggerSplit(4);
		setTimeout(() => {
			triggerTabPress();
		}, 0);
	}

	function handleTripleHellKey(event: KeyboardEvent): void {
		event.preventDefault();
		triggerSplit(3);
		triggerTabPress();
		triggerSplit(4);
		setTimeout(() => {
			triggerTabPress();
		}, 0);
	}

	function handleRailgunKey(event: KeyboardEvent): void {
		event.preventDefault();
		let originalMove: typeof sigfix.net.move | undefined;
		let restoreTimeout: ReturnType<typeof setTimeout> | undefined;

		// Salva a função original e a desativa
		originalMove = sigfix.net.move;
		sigfix.net.move = () => {};

		triggerSplit(3);

		// Restaura a função original após 750ms
		restoreTimeout = setTimeout(() => {
			if (originalMove) {
				sigfix.net.move = originalMove;
			}
			originalMove = undefined;
			restoreTimeout = undefined;
		}, 750);
	}

	// Função para atualizar os keyHandlers após as configurações mudarem
	let keyHandlers: Record<string, KeyHandler> = {};

	function updateKeyHandlers(): void {
		keyHandlers = {
			[settings[STORAGE_KEYS.double]?.toLowerCase()]: handleDoubleHellKey,
			[settings[STORAGE_KEYS.triple]?.toLowerCase()]: handleTripleHellKey,
			[settings[STORAGE_KEYS.railgun]?.toLowerCase()]: handleRailgunKey,
		};
	}

	// Inicialização dos keyHandlers após as configurações serem carregadas
	function initializeKeyHandlers(): void {
		updateKeyHandlers();
	}

	// Manipulador de eventos para keydown global
	function handleGlobalKeydown(event: KeyboardEvent): void {
		if (isMacroDisabled) return;

		const pressedKey = event.key?.toLowerCase();
		const keyHandler = keyHandlers[pressedKey];
		if (keyHandler) {
			keyHandler(event);
		}
	}

	// Função para esperar elementos na DOM
	function waitForElements(selectors: string[], callback: () => void, intervalTime = 500, maxAttempts = 20): void {
		let attempts = 0;
		const interval = setInterval(() => {
			const allPresent = selectors.every(selector => document.querySelector(selector));
			if (allPresent) {
				clearInterval(interval);
				callback();
			} else {
				attempts++;
				if (attempts >= maxAttempts) {
					clearInterval(interval);
					console.warn(`waitForElements: Elementos não encontrados após ${maxAttempts} tentativas.`);
				}
			}
		}, intervalTime);
	}

	// Desativa macros quando o input de chat está focado
	function monitorChatInput(): void {
		const interval = setInterval(() => {
			const chatInput = document.querySelector(SELECTORS.chatInput) as HTMLInputElement | null;
			if (chatInput) {
				clearInterval(interval);

				const disableIndicator = createElement("div", {
					styles: {
						display: "none",
						position: "fixed",
						bottom: "10px",
						right: "10px",
						padding: "5px 10px",
						backgroundColor: "rgba(0,0,0,0.7)",
						color: "#fff",
						borderRadius: "5px",
						zIndex: "1000",
					},
				});
				disableIndicator.id = "brasil-disable-indicator";
				disableIndicator.textContent = "Desativado durante o chat";
				document.body.appendChild(disableIndicator);

				chatInput.addEventListener("focus", () => {
					isMacroDisabled = true;
					disableIndicator.style.display = "block";
				});

				chatInput.addEventListener("blur", () => {
					isMacroDisabled = false;
					disableIndicator.style.display = "none";
				});
			}
		}, 500);
	}

	// Função de inicialização
	function initialize(): void {
		loadSettings();
		initializeKeyHandlers();
		waitForElements([SELECTORS.navbar, SELECTORS.menuContent], createConfigContainer);
		document.addEventListener("keydown", handleGlobalKeydown);
		monitorChatInput();
		console.log("BrasilScripts carregado com sucesso.");
	}

	// Inicia o script
	initialize();
})();
