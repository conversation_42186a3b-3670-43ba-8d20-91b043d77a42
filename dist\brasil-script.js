// ==UserScript==
// @name         SigPlus+ 🇧🇷
// @namespace    SigPlus+ 🇧🇷
// @version      1.0.28
// <AUTHOR>
// @description  Script for InfernoTrick by brazilians
// @license MIT
// @match        https://*.sigmally.com/*
// @icon         https://i.imgur.com/kgTrxZy.gif
// @grant        none
// @run-at       document-idle
// ==/UserScript==

// @ts-ignore
var sigfix = window.sigfix;
// Constantes de tempo
var ANIMATION_TIME = {
    FADE_OUT: 200,
    FADE_IN: 10,
};
// Constantes de duração
var DURATIONS = {
    DISABLE_MOVE: 750,
    INPUT_CHECK_INTERVAL: 500,
    WAIT_ELEMENTS_INTERVAL: 500,
};
// Tipos utilitários
var STORAGE_KEYS = {
    double: "doubleKey",
    triple: "tripleKey",
};
// Utilitário de storage
var storageService = {
    get: function (key) {
        var value = localStorage.getItem(key);
        return value ? value : undefined;
    },
    set: function (key, value) {
        localStorage.setItem(key, value);
    },
    remove: function (key) {
        localStorage.removeItem(key);
    },
    getJSON: function (key) {
        var raw = this.get(key);
        if (!raw)
            return undefined;
        try {
            return JSON.parse(raw);
        }
        catch (error) {
            console.error("Erro ao fazer parse do JSON de ".concat(key, ":"), error);
            return undefined;
        }
    },
};
// Estado
var settings = {};
var isMacroDisabled = false;
// Seletores
var SELECTORS = {
    navbar: ".mod_menu_navbar",
    menuContent: ".mod_menu_content",
    chatInput: "#chatSendInput",
    nickInput: "#nick",
    tagInput: "#tag",
};
// Utilitário de criação de elementos
function createElement(tag, options) {
    if (options === void 0) { options = {}; }
    var el = document.createElement(tag);
    if (options.className)
        el.className = options.className;
    if (options.textContent)
        el.textContent = options.textContent;
    if (options.styles)
        Object.assign(el.style, options.styles);
    if (options.attributes) {
        Object.entries(options.attributes).forEach(function (_a) {
            var k = _a[0], v = _a[1];
            return el.setAttribute(k, v);
        });
    }
    return el;
}
// Carregar/salvar configurações
function loadSettings() {
    Object.values(STORAGE_KEYS).forEach(function (key) {
        var value = storageService.get(key);
        if (value)
            settings[key] = value.toLowerCase();
    });
}
function saveSettings() {
    Object.values(STORAGE_KEYS).forEach(function (key) {
        if (settings[key]) {
            storageService.set(key, settings[key]);
        }
        else {
            storageService.remove(key);
        }
    });
}
// Keybinds dinâmicos
var keybinds = [
    {
        property: STORAGE_KEYS.double,
        title: "Double Hover Key (2-4):",
        helpText: "Send Double HoverSplit",
    },
    {
        property: STORAGE_KEYS.triple,
        title: "Triple Hover Key (3-4):",
        helpText: "Send Triple(straight) HoverSplit",
    },
];
// Handlers dinâmicos
var keyHandlers = {};
function updateKeyHandlers() {
    var _a, _b;
    // Limpar handlers existentes primeiro
    Object.keys(keyHandlers).forEach(function (key) { return delete keyHandlers[key]; });
    // Adicionar novos handlers
    var doubleKey = (_a = settings[STORAGE_KEYS.double]) === null || _a === void 0 ? void 0 : _a.toLowerCase();
    var tripleKey = (_b = settings[STORAGE_KEYS.triple]) === null || _b === void 0 ? void 0 : _b.toLowerCase();
    if (doubleKey)
        keyHandlers[doubleKey] = handleDoubleHellKey;
    if (tripleKey)
        keyHandlers[tripleKey] = handleTripleHellKey;
}
// Funções de macro
var DEFAULT_KEY_EVENT_PROPS = {
    key: " ",
    code: "Space",
    keyCode: 32,
    which: 32,
    cancelable: true,
    composed: true,
};
function simulateKeyPress(eventProps) {
    window.dispatchEvent(new KeyboardEvent("keydown", eventProps));
    window.dispatchEvent(new KeyboardEvent("keyup", eventProps));
}
function triggerSplit(times) {
    if (times <= 0)
        return;
    simulateKeyPress(DEFAULT_KEY_EVENT_PROPS);
    triggerSplit(times - 1);
}
function triggerTabPress() {
    var view = sigfix.world.selected === sigfix.world.viewId.primary ? sigfix.world.viewId.secondary : sigfix.world.viewId.primary;
    sigfix.input.tab(view);
}
// Macro: desabilitar temporariamente movimento
var isDisabledMove = false;
var disableCountMove = 0;
var originalMove;
/**
 * Desabilita temporariamente o movimento e executa uma ação
 * @param duration Duração em milissegundos para desabilitar o movimento
 * @param action Função a ser executada enquanto o movimento está desabilitado
 */
function temporarilyDisableMove(duration, action) {
    if (!isDisabledMove) {
        originalMove = sigfix.net.move;
        sigfix.net.move = function () { };
        isDisabledMove = true;
    }
    disableCountMove++;
    try {
        action();
    }
    catch (error) {
        console.error("Erro durante execução da ação com movimento desabilitado:", error);
    }
    finally {
        setTimeout(function () {
            disableCountMove--;
            if (disableCountMove === 0 && originalMove) {
                sigfix.net.move = originalMove;
                isDisabledMove = false;
                originalMove = undefined;
            }
        }, duration);
    }
}
// Handlers
/**
 * Handler para a tecla de HoverSplit Duplo
 */
function handleDoubleHellKey(event) {
    event.preventDefault();
    executeHoverSplitSequence(2, 4);
}
/**
 * Handler para a tecla de HoverSplit Triplo
 */
function handleTripleHellKey(event) {
    event.preventDefault();
    executeHoverSplitSequence(3, 4);
}
/**
 * Executa uma sequência de HoverSplit
 * @param initialSplits Número de splits iniciais
 * @param secondarySplits Número de splits secundários após alternar a tab
 */
function executeHoverSplitSequence(initialSplits, secondarySplits) {
    temporarilyDisableMove(DURATIONS.DISABLE_MOVE, function () {
        triggerSplit(initialSplits);
        triggerTabPress();
        triggerSplit(secondarySplits);
        setTimeout(function () { return triggerTabPress(); }, 0);
    });
}
// UI: container principal
var brasilScriptsContainer = createElement("div", {
    className: "brasil-scripts-container mod_tab scroll",
    styles: { display: "none" },
});
function createTitle(text) {
    brasilScriptsContainer.appendChild(createElement("div", { className: "text-center", textContent: text }));
}
function createCategory(_a) {
    var title = _a.title, emoji = _a.emoji;
    var categoryContainer = createElement("div", { styles: { display: "flex", alignItems: "center", margin: "10px 0" } });
    categoryContainer.appendChild(createElement("span", { textContent: emoji, styles: { marginRight: "8px", fontSize: "1.2em" } }));
    categoryContainer.appendChild(createElement("span", { textContent: title, styles: { fontWeight: "bold", marginRight: "8px" } }));
    categoryContainer.appendChild(createElement("div", { styles: { flexGrow: "1", height: "1px", backgroundColor: "#bfbfbf", marginLeft: "8px" } }));
    brasilScriptsContainer.appendChild(categoryContainer);
}
function getSigmodUsedKeys() {
    var _a;
    var sigmodKeys = [];
    try {
        var settings_1 = storageService.getJSON("SigModClient-settings");
        if (!((_a = settings_1 === null || settings_1 === void 0 ? void 0 : settings_1.macros) === null || _a === void 0 ? void 0 : _a.keys))
            return sigmodKeys;
        var keys = settings_1.macros.keys;
        // Função auxiliar para processar chaves
        var processKeys_1 = function (keyList) {
            keyList.filter(function (k) { return typeof k === "string" && k.length === 1; }).forEach(function (k) { return sigmodKeys.push(k.toLowerCase()); });
        };
        // Chaves diretas
        processKeys_1([keys.rapidFeed, keys.respawn, keys.location, keys.saveImage]);
        // Processar grupos de chaves
        var processKeyGroup = function (group) {
            if (group && typeof group === "object") {
                processKeys_1(Object.values(group));
            }
        };
        // Processar cada grupo
        processKeyGroup(keys.splits);
        processKeyGroup(keys.line);
        processKeyGroup(keys.toggle);
        console.log("Teclas usadas pelo SigMod:", sigmodKeys);
    }
    catch (error) {
        console.error("Erro ao obter teclas usadas pelo SigMod:", error);
    }
    return sigmodKeys;
}
function createKeybindInput(_a) {
    var _b, _c;
    var property = _a.property, title = _a.title, helpText = _a.helpText;
    var inputContainer = createElement("div", {
        className: "keybind-input-container modRowItems justify-sb",
        styles: { position: "relative", padding: "5px 10px" },
    });
    inputContainer.title = helpText;
    inputContainer.appendChild(createElement("span", { textContent: title }));
    var input = createElement("input", {
        className: "keybind-input modInput",
        styles: { display: "flex", justifyContent: "center", textAlign: "center", alignItems: "center", width: "50px" },
        attributes: { type: "text", id: "brasil-".concat(property), placeholder: "..." },
    });
    input.value = (_c = (_b = settings[property]) === null || _b === void 0 ? void 0 : _b.toUpperCase()) !== null && _c !== void 0 ? _c : "";
    input.addEventListener("keydown", function (e) {
        e.preventDefault();
        var key = e.key;
        var keyLower = key.toLowerCase();
        if (key === "Escape" || key === "Backspace") {
            settings[property] = "";
            input.value = "";
            saveSettings();
            updateKeyHandlers();
            return;
        }
        var isConflict = keybinds.some(function (kb) { var _a; return kb.property !== property && ((_a = settings[kb.property]) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === keyLower; });
        var sigmodUsed = getSigmodUsedKeys();
        if (isConflict) {
            alert("The key \"".concat(key, "\" is already assigned to another command."));
            return;
        }
        if (sigmodUsed.includes(keyLower)) {
            alert("The key \"".concat(key, "\" is already used by SigMod! Please choose another one."));
            return;
        }
        settings[property] = keyLower;
        input.value = key.toUpperCase();
        saveSettings();
        updateKeyHandlers();
    });
    inputContainer.appendChild(input);
    brasilScriptsContainer.appendChild(inputContainer);
}
// UI: botão de navegação
function createNavigationButton(navigationMenu) {
    var navButton = createElement("button", { className: "brasil-nav-btn mod_nav_btn" });
    navButton.appendChild(createElement("img", {
        styles: { width: "20px", height: "20px", verticalAlign: "middle", borderRadius: "50%" },
        attributes: { src: "https://imgur.com/caiOSAM.png", alt: "Ícone Brasil" },
    }));
    navButton.appendChild(createElement("span", { textContent: "Sig Plus+" }));
    navigationMenu.appendChild(navButton);
    navButton.addEventListener("click", function () {
        document.querySelectorAll(".mod_tab").forEach(function (tab) {
            var tabElement = tab;
            tabElement.style.opacity = "0";
            setTimeout(function () {
                tabElement.style.display = "none";
            }, ANIMATION_TIME.FADE_OUT);
        });
        document.querySelectorAll(".mod_nav_btn").forEach(function (btn) { return btn.classList.remove("mod_selected"); });
        navButton.classList.add("mod_selected");
        setTimeout(function () {
            brasilScriptsContainer.style.display = "flex";
            setTimeout(function () {
                brasilScriptsContainer.style.opacity = "1";
            }, ANIMATION_TIME.FADE_IN);
        }, ANIMATION_TIME.FADE_OUT);
    });
}
// UI: configuração
function createConfigContainer() {
    var navigationMenu = document.querySelector(SELECTORS.navbar);
    var menuContent = document.querySelector(SELECTORS.menuContent);
    if (!navigationMenu || !menuContent)
        return;
    navigationMenu.style.gap = "8px";
    createNavigationButton(navigationMenu);
    menuContent.appendChild(brasilScriptsContainer);
    createTitle("Sigmally 🇧🇷 Scripts");
    createCategory({ title: "HoverSplit", emoji: "✨" });
    keybinds.forEach(createKeybindInput);
}
// Macro: monitorar inputs para desativar macro
function monitorChatInput() {
    var inputSelectors = [SELECTORS.chatInput, SELECTORS.nickInput, SELECTORS.tagInput];
    var focusCount = 0;
    var createDisableIndicator = function () {
        var indicator = createElement("div", {
            styles: {
                display: "none",
                position: "fixed",
                bottom: "10px",
                right: "10px",
                padding: "5px 10px",
                backgroundColor: "rgba(192, 33, 33, 0.7)",
                color: "#fff",
                borderRadius: "5px",
                zIndex: "1000",
            },
        });
        indicator.id = "brasil-disable-indicator";
        indicator.textContent = "Desativado durante o chat";
        document.body.appendChild(indicator);
        return indicator;
    };
    var handleInputFocus = function (indicator) {
        focusCount++;
        if (focusCount === 1) {
            isMacroDisabled = true;
            indicator.style.display = "block";
        }
    };
    var handleInputBlur = function (indicator) {
        focusCount--;
        if (focusCount === 0) {
            isMacroDisabled = false;
            indicator.style.display = "none";
        }
    };
    var interval = setInterval(function () {
        var inputs = inputSelectors.flatMap(function (sel) { return Array.from(document.querySelectorAll(sel)); });
        if (inputs.length > 0) {
            clearInterval(interval);
            var disableIndicator_1 = createDisableIndicator();
            inputs.forEach(function (input) {
                input.addEventListener("focus", function () { return handleInputFocus(disableIndicator_1); });
                input.addEventListener("blur", function () { return handleInputBlur(disableIndicator_1); });
            });
        }
    }, DURATIONS.INPUT_CHECK_INTERVAL);
}
// Inicialização
function waitForElements(selectors, callback, intervalTime, maxAttempts) {
    if (intervalTime === void 0) { intervalTime = DURATIONS.WAIT_ELEMENTS_INTERVAL; }
    if (maxAttempts === void 0) { maxAttempts = 20; }
    var attempts = 0;
    var interval = setInterval(function () {
        var allPresent = selectors.every(function (selector) { return document.querySelector(selector); });
        if (allPresent) {
            clearInterval(interval);
            callback();
        }
        else {
            attempts++;
            if (attempts >= maxAttempts) {
                clearInterval(interval);
                console.warn("waitForElements: Elementos n\u00E3o encontrados ap\u00F3s ".concat(maxAttempts, " tentativas."));
            }
        }
    }, intervalTime);
}
function handleGlobalKeydown(event) {
    var _a;
    if (isMacroDisabled)
        return;
    var pressedKey = (_a = event.key) === null || _a === void 0 ? void 0 : _a.toLowerCase();
    var keyHandler = keyHandlers[pressedKey];
    if (keyHandler)
        keyHandler(event);
}
function initialize() {
    loadSettings();
    updateKeyHandlers();
    waitForElements([SELECTORS.navbar, SELECTORS.menuContent], createConfigContainer);
    document.addEventListener("keydown", handleGlobalKeydown);
    monitorChatInput();
}
// Inicia o script
initialize();